import axios from 'axios';

const OPENROUTER_API_KEY = '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';

async function checkClaudeModels() {
  try {
    console.log('🔍 Fetching all models from OpenRouter...');
    
    const response = await axios.get('https://openrouter.ai/api/v1/models', {
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data && response.data.data && Array.isArray(response.data.data)) {
      const allModels = response.data.data
        .filter(model => model.enabled !== false)
        .map(model => model.id);

      console.log(`\n📊 Total models available: ${allModels.length}`);

      // Find Claude models
      const claudeModels = allModels.filter(model => 
        model.toLowerCase().includes('claude') || 
        model.toLowerCase().includes('anthropic')
      );

      console.log(`\n🤖 Claude/Anthropic models available (${claudeModels.length}):`);
      claudeModels.forEach(model => console.log(`  - ${model}`));

      // Find code-related models
      const codeModels = allModels.filter(model => 
        model.toLowerCase().includes('code') || 
        model.toLowerCase().includes('deepseek') ||
        model.toLowerCase().includes('qwen') ||
        model.toLowerCase().includes('coder')
      );

      console.log(`\n💻 Code-related models available (${codeModels.length}):`);
      codeModels.slice(0, 10).forEach(model => console.log(`  - ${model}`));
      if (codeModels.length > 10) {
        console.log(`  ... and ${codeModels.length - 10} more`);
      }

      // Check specific models from our JSON
      const ourModels = [
        "anthropic/claude-4-sonnet",
        "anthropic/claude-3-sonnet", 
        "anthropic/claude-3.5-sonnet",
        "openai/o3",
        "openai/gpt-4.1",
        "google/gemini-2.5-pro-preview"
      ];

      console.log(`\n🎯 Checking our configured models:`);
      ourModels.forEach(model => {
        const available = allModels.includes(model);
        console.log(`  ${available ? '✅' : '❌'} ${model}`);
      });

    } else {
      console.error('❌ Invalid response format from OpenRouter');
    }
  } catch (error) {
    console.error('❌ Error fetching models:', error.message);
  }
}

checkClaudeModels();
