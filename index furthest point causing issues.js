import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import axios from 'axios';
import fs from 'fs';
import path from 'path';

// OpenRouter API configuration
const OPENROUTER_API_ENDPOINT = 'https://openrouter.ai/api/v1';
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || '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';

// Fallback API endpoint (The Guru's own API)
const GURU_API_ENDPOINT = 'https://63660191-5465-48d1-a384-83fe8ffc5b94-00-k5l1b9vlgd3n.janeway.replit.dev/api/v1/mcp';
const GURU_API_KEY = 'Bearer 9ea0e4f8868020aacf2479d1b4514bee';

// Path to the Best LLM per category document
// Consider using path.join(__dirname, 'data', 'best_llm_per_category.json') for robustness if your script isn't always run from project root
const BEST_LLM_PATH = path.join(process.cwd(), 'data', 'best_llm_per_category.json');

class GuruAIServer {
  constructor() {
    this.server = new Server(
      {
        name: 'The Guru AI',
        version: '1.0.0',
        description: 'An intelligent MCP server that provides access to the best AI models for any task',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    // Initialize API clients
    this.openrouterClient = axios.create({
      baseURL: OPENROUTER_API_ENDPOINT,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://theguru.ai', // Replace with your actual site if needed
        'X-Title': 'The Guru AI', // Replace with your actual app name if needed
      },
    });

    this.guruClient = axios.create({
      baseURL: GURU_API_ENDPOINT,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': GURU_API_KEY,
      },
    });

    // Model information
    this.availableModels = []; // Will be populated on startup
    this.modelCategories = {}; // Will be loaded from best_llm_per_category.json
    
    this.server.onerror = (error) => console.error('[Guru MCP Error]', error);
    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  // Load the Best LLM per category document
  async loadBestLLMCategories() {
    // Define the new default categories based on your provided JSON
    const userDefinedDefaultCategories = {
        "overall_performance": ["google/gemini-2.5-pro-preview"], // As of May 2025, ensure this model ID is accurate for OpenRouter
        "advanced_reasoning": ["openai/o3"], // This will map to openai/gpt-4o
        "code_generation": ["anthropic/claude-4-sonnet"], // As of May 2025, ensure this is the correct ID on OpenRouter
        "language_understanding": ["openai/gpt-4.1"], // This will map to openai/gpt-4o
        "mathematical_scientific": ["openai/o3", "openai/o3-mini"], // Map to gpt-4o, gpt-4o-mini
        "creative_content": ["google/gemini-2.5-pro"], // As of May 2025, ensure this is the correct ID
        "conversational_fluency": ["google/gemini-2.5-pro-preview"],
        "multilingual_proficiency": ["google/gemini-2.0-flash"], // Ensure correct ID
        "instruction_adherence": ["openai/o1-mini"], // Maps to gpt-4o-mini
        "truthfulness_factual": ["openai/o1", "openai/o3-mini-high"], // Map to gpt-4o, gpt-4o-mini
        "ai_safety_ethics": ["openai/o3"] // Maps to gpt-4o
      };

    try {
      if (fs.existsSync(BEST_LLM_PATH)) {
        const data = fs.readFileSync(BEST_LLM_PATH, 'utf8');
        this.modelCategories = JSON.parse(data);
        console.error(`[Guru] Loaded ${Object.keys(this.modelCategories).length} model categories from ${BEST_LLM_PATH}`);
      } else {
        console.error(`[Guru] ${BEST_LLM_PATH} not found. Using user-defined default model categories.`);
        this.modelCategories = userDefinedDefaultCategories;
        
        if (!fs.existsSync(path.dirname(BEST_LLM_PATH))) {
          fs.mkdirSync(path.dirname(BEST_LLM_PATH), { recursive: true });
        }
        
        fs.writeFileSync(BEST_LLM_PATH, JSON.stringify(this.modelCategories, null, 2));
        console.error(`[Guru] Created default model categories file at ${BEST_LLM_PATH} with user-defined defaults.`);
      }
    } catch (error) {
      console.error(`[Guru] Error loading/creating best LLM categories from ${BEST_LLM_PATH}: ${error.message}`);
      this.modelCategories = userDefinedDefaultCategories;
      console.error(`[Guru] Using user-defined default model categories due to error.`);
    }
  }

  async fetchAvailableModels() {
    try {
      console.error(`[Guru] Fetching available models from OpenRouter...`);
      const response = await this.openrouterClient.get('/models');
      
      if (response.data?.data && Array.isArray(response.data.data)) {
        this.availableModels = response.data.data
          .filter(model => model.enabled !== false)
          .map(model => model.id);
        console.error(`[Guru] Loaded ${this.availableModels.length} available models from OpenRouter`);
      } else {
        throw new Error('Invalid response format from OpenRouter');
      }
    } catch (error) {
      console.error(`[Guru] Error fetching models from OpenRouter: ${error.message}`);
      try {
        console.error(`[Guru] Attempting to fetch models from Guru API fallback...`);
        const response = await axios.get(
          'https://63660191-5465-48d1-a384-83fe8ffc5b94-00-k5l1b9vlgd3n.janeway.replit.dev/api/models',
          { headers: { 'Content-Type': 'application/json' } }
        );
        if (response.data && Array.isArray(response.data)) {
          this.availableModels = response.data
            .filter(model => model.available)
            .map(model => model.name);
          console.error(`[Guru] Loaded ${this.availableModels.length} available models from Guru API`);
        } else {
          throw new Error('Invalid response format from Guru API');
        }
      } catch (fallbackError) {
        console.error(`[Guru] Error fetching models from Guru API: ${fallbackError.message}`);
        this.availableModels = [ // Ensure these are up-to-date for May 2025
          "openai/gpt-4o", "anthropic/claude-3-opus", "anthropic/claude-3.5-sonnet", "anthropic/claude-3-haiku",
          "google/gemini-1.5-pro", "google/gemini-1.5-flash", "meta-llama/llama-3-70b-instruct",
          "meta-llama/llama-3-8b-instruct", "mistralai/mistral-large-latest", "mistralai/mixtral-8x7b",
          // Potentially newer models for May 2025:
          // "anthropic/claude-4-opus", "anthropic/claude-4-sonnet", 
          // "google/gemini-2.5-pro",
        ];
        console.error(`[Guru] Using hardcoded fallback list of ${this.availableModels.length} models`);
      }
    }
     if (this.availableModels.length === 0) {
        console.warn("[Guru] Warning: No available models were loaded. Model selection may fail.");
    }
  }

  // Select the best model for a given task
  selectBestModel(task, preferredModel = null) {
    // If user specified a model and it's valid, use it
    if (preferredModel) {
        if (this.isValidModel(preferredModel)) {
            console.error(`[Guru] Using preferred model: ${preferredModel}`);
            return preferredModel;
        } else {
            console.warn(`[Guru] Preferred model "${preferredModel}" is not valid or available. Ignoring and selecting based on task.`);
        }
    }

    const taskToCategoryMap = {
      'writing': 'creative_content', 'poem': 'creative_content', 'story': 'creative_content', 'creative': 'creative_content',
      'code': 'code_generation', 'programming': 'code_generation',
      'math': 'mathematical_scientific', 'science': 'mathematical_scientific', 'calculation': 'mathematical_scientific',
      'question': 'language_understanding',
      'factual': 'truthfulness_factual',
      'reasoning': 'advanced_reasoning', 'problem': 'advanced_reasoning',
      'conversation': 'conversational_fluency', 'chat': 'conversational_fluency',
      'translate': 'multilingual_proficiency', 'language': 'multilingual_proficiency',
      'instruction': 'instruction_adherence',
      'ethics': 'ai_safety_ethics', 'safety': 'ai_safety_ethics'
    };

    let category = 'overall_performance';
    const taskLower = task.toLowerCase();
    for (const [keyword, mappedCategory] of Object.entries(taskToCategoryMap)) {
      if (taskLower.includes(keyword)) {
        category = mappedCategory;
        break;
      }
    }
    console.error(`[Guru] Task (first 50 chars): "${task.substring(0,50)}..." mapped to category "${category}"`);

    const modelsForCategory = this.modelCategories[category] || this.modelCategories['overall_performance'] || ['anthropic/claude-3-sonnet']; // Default to a known good model
    if (!this.modelCategories[category] && category !== 'overall_performance') {
        if (!this.modelCategories.hasOwnProperty(category)) {
            console.warn(`[Guru] Category key "${category}" not found in modelCategories. Using "overall_performance" or hardcoded default list.`);
        } else if (!this.modelCategories[category] || this.modelCategories[category].length === 0) {
             console.warn(`[Guru] Category "${category}" found in modelCategories but is empty. Using "overall_performance" or hardcoded default list.`);
        }
    }
    
    // Revised modelMapping:
    // - Kept OpenAI aliases as they map shorthand/internal names to actual IDs.
    // - Removed mappings that appeared to downgrade potentially current model IDs
    //   (like google/gemini-2.5-pro) to older versions.
    // The principle is: if a model ID is current and you want to use it,
    // list it directly in best_llm_per_category.json. This map is for aliases
    // or truly old names that need forwarding to a current equivalent.
    const modelMapping = {
      'openai/o3': 'openai/gpt-4o',                 // Alias for gpt-4o
      'openai/o1': 'openai/gpt-4o',                 // Alias for gpt-4o
      'openai/o3-mini': 'openai/gpt-4o-mini',       // Alias for gpt-4o-mini
      'openai/o3-mini-high': 'openai/gpt-4o-mini',  // Alias for gpt-4o-mini
      'openai/o1-mini': 'openai/gpt-4o-mini',       // Alias for gpt-4o-mini
      'openai/gpt-4.1': 'openai/gpt-4o',            // Older alias potentially pointing to current gpt-4o

      // REMOVED: 'anthropic/claude-4-sonnet': 'anthropic/claude-3-opus'
      // REMOVED: 'google/gemini-2.5-pro-preview': 'google/gemini-1.5-pro'
      // REMOVED: 'google/gemini-2.5-pro': 'google/gemini-1.5-pro'
      // REMOVED: 'google/gemini-2.0-flash': 'google/gemini-1.5-flash'
      // These were removed because if the keys (e.g., 'google/gemini-2.5-pro') represent
      // actual, current, and desired model IDs available on OpenRouter, this mapping
      // would incorrectly force them to use an older version.
      // Your `best_llm_per_category.json` should list the direct, current OpenRouter model IDs.
    };

    for (const model of modelsForCategory) {
      const mappedModel = modelMapping[model] || model; 
      if (this.isValidModel(mappedModel)) {
        if (mappedModel !== model) {
            console.error(`[Guru] Selected model for category ${category}: ${mappedModel} (original from list: ${model} was mapped)`);
        } else {
            console.error(`[Guru] Selected model for category ${category}: ${mappedModel} (from list)`);
        }
        return mappedModel;
      } else {
        console.warn(`[Guru] Model ${mappedModel} (original from list: ${model}) from category list for "${category}" is not in availableModels. Skipping.`);
      }
    }

    // Fallback model name - ensure this is a generally available and good model for May 2025
    const defaultFallbackModel = 'anthropic/claude-3.5-sonnet'; // Or another strong, widely available model
    if (this.isValidModel(defaultFallbackModel)) {
        console.error(`[Guru] Fallback: Category-based selection failed or models not available. Using default fallback model '${defaultFallbackModel}' as it's available.`);
        return defaultFallbackModel;
    } else if (this.availableModels.length > 0) {
        const firstAvailableModel = this.availableModels[0];
        console.error(`[Guru] Fallback: Default fallback model '${defaultFallbackModel}' not available. Using first available model from list: ${firstAvailableModel}`);
        return firstAvailableModel;
    } else {
        console.error(`[Guru] Critical Fallback: No models in availableModels list, and default fallback '${defaultFallbackModel}' is not listed/valid. Returning '${defaultFallbackModel}' as absolute last resort (may not be usable).`);
        return defaultFallbackModel;
    }
  }
  
  parseGuruCommand(prompt) {
    const guruPrefix = /^guru,\s*/i;
    if (!guruPrefix.test(prompt)) {
      return { isGuruCommand: false, model: null, prompt: prompt };
    }
    const withoutPrefix = prompt.replace(guruPrefix, '');
    for (const model of this.availableModels) {
      const escapedModel = model.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const modelPrefix = new RegExp(`^${escapedModel}[,:]\\s*`, 'i');
      if (modelPrefix.test(withoutPrefix)) {
        return {
          isGuruCommand: true,
          model: model,
          prompt: withoutPrefix.replace(modelPrefix, '')
        };
      }
    }
    return { isGuruCommand: true, model: null, prompt: withoutPrefix };
  }

  isWorkflowRequest(prompt) {
    const workflowKeywords = [
      'step by step', 'workflow', 'multi-step', 'sequence', 'chain', 'pipeline', 'process'
    ];
    return workflowKeywords.some(keyword => prompt.toLowerCase().includes(keyword));
  }

  async executeWorkflow(prompt) {
    const workflowPlanPrompt = `
      I need to break down the following task into a sequence of steps, where each step might benefit 
      from a different specialized AI model. For each step, specify the task category 
      (e.g., creative_content, code_generation, truthfulness_factual, summarization, multilingual_proficiency, or overall_performance).
      Task: ${prompt}
      Format your response as a JSON array of steps, each with 'description' and 'category' fields.
      Example: [{"description": "Research key facts about X", "category": "truthfulness_factual"}, ...]
    `;
    try {
      const planningModel = this.selectBestModel("Plan a complex task", "anthropic/claude-3.5-sonnet"); // Or your preferred planner
      const planResponse = await this.callOpenRouterModel(planningModel, workflowPlanPrompt);
      const jsonMatch = planResponse.match(/\[[\s\S]*\]/);
      if (!jsonMatch) throw new Error("Failed to generate a valid JSON workflow plan");
      
      const workflowSteps = JSON.parse(jsonMatch[0]);
      if (!Array.isArray(workflowSteps) || workflowSteps.length === 0) throw new Error("Workflow plan is empty or not an array.");
      
      let stepResults = [];
      let contextSoFar = "";
      for (let i = 0; i < workflowSteps.length; i++) {
        const step = workflowSteps[i];
        if (!step || typeof step.description !== 'string') {
          console.warn(`[Guru] Skipping invalid workflow step ${i+1}:`, step);
          continue;
        }
        const stepPrompt = `Task: ${step.description}\n${contextSoFar ? `Context from previous steps:\n${contextSoFar}` : ''}\nPlease complete this specific step of the larger task.`;
        const taskForModelSelection = step.category ? `${step.description} (Hint: category is ${step.category})` : step.description;
        const bestModel = this.selectBestModel(taskForModelSelection);
        console.error(`[Guru] Workflow Step ${i+1}: "${step.description.substring(0,50)}...". Selected model: ${bestModel}`);
        const stepResult = await this.callOpenRouterModel(bestModel, stepPrompt);
        stepResults.push({ step: step.description, model: bestModel, result: stepResult });
        contextSoFar += `\n\nStep ${i+1} ("${step.description}") result:\n${stepResult}`;
      }
      let finalResponse = `I've completed your task "${prompt.substring(0,100)}..." as a multi-step workflow:\n\n`;
      stepResults.forEach((result, index) => {
        finalResponse += `Step ${index+1}: ${result.step}\n(Using ${result.model})\n${result.result}\n\n`;
      });
      return finalResponse;
    } catch (error) {
      console.error(`[Guru] Workflow execution error: ${error.message}. Falling back to single model execution.`);
      const bestModel = this.selectBestModel(prompt);
      return await this.callOpenRouterModel(bestModel, prompt);
    }
  }

  async callOpenRouterModel(model, prompt) {
    try {
      console.error(`[Guru] Calling model ${model} through OpenRouter for prompt (first 50 chars): "${prompt.substring(0,50)}..."`);
      const response = await this.openrouterClient.post('/chat/completions', {
        model: model,
        messages: [{ role: "user", content: prompt }],
        temperature: 0.7,
        max_tokens: 4000 // Increased max_tokens, adjust as needed
      });
      if (response.data?.choices?.[0]?.message?.content) {
        return response.data.choices[0].message.content;
      } else {
        throw new Error('Invalid or empty response format from OpenRouter');
      }
    } catch (error) {
      let errorMessage = error.message;
      if (error.response?.data?.error?.message) {
        errorMessage = error.response.data.error.message;
      } else if (error.response?.statusText) {
        errorMessage = `${error.response.status} ${error.response.statusText}`;
      }
      console.error(`[Guru] OpenRouter API error for model ${model}: ${errorMessage}`);
      try {
        console.warn(`[Guru] OpenRouter failed for ${model}. Attempting fallback with The Guru API...`);
        const formattedPrompt = `Guru, ${model}: ${prompt}`;
        const fallbackResponse = await this.guruClient.post('/', { prompt: formattedPrompt });
        if (fallbackResponse.data?.response) {
          return fallbackResponse.data.response;
        } else {
          throw new Error('Invalid or empty response format from The Guru API fallback');
        }
      } catch (fallbackError) {
        console.error(`[Guru] Fallback API error: ${fallbackError.message}`);
        return `I apologize, but I encountered an error while processing your request with model ${model}. OpenRouter error: (${errorMessage}). Fallback API error: (${fallbackError.message}).`;
      }
    }
  }

  isValidModel(model) {
    if (!model) return false;
    return this.availableModels.includes(model);
  }

  async handleStreamingResponse(response) {
    try {
      console.error('[Guru] Processing streaming response...');
      let fullResponse = '';
      return new Promise((resolve, reject) => {
        let buffer = '';
        response.data.on('data', (chunk) => {
          buffer += chunk.toString();
          while (buffer.includes('\n\n')) {
            const endOfLine = buffer.indexOf('\n\n');
            const message = buffer.substring(0, endOfLine);
            buffer = buffer.substring(endOfLine + 2);
            if (message.startsWith('data: ')) {
              const jsonData = message.substring(6).trim();
              if (jsonData === '[DONE]') {
                continue; 
              }
              try {
                const data = JSON.parse(jsonData);
                if (data.choices?.[0]?.delta?.content) {
                  fullResponse += data.choices[0].delta.content;
                }
              } catch (err) {
                 console.error(`[Guru] Error parsing SSE JSON data: "${jsonData}". Error: ${err.message}.`);
              }
            }
          }
        });
        response.data.on('error', (err) => {
          console.error(`[Guru] Stream error: ${err.message}`);
          reject(new Error(`Stream error: ${err.message}`));
        });
        response.data.on('end', () => {
          console.error(`[Guru] Stream ended. Full response length: ${fullResponse.length}`);
          resolve(fullResponse);
        });
      });
    } catch (error) {
      console.error(`[Guru] Error in handleStreamingResponse: ${error.message}`);
      throw error;
    }
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'guru_ai_request',
          description: 'Make a request to The Guru AI service with intelligent model selection. Supports direct model specification and task-based model routing.',
          inputSchema: {
            type: 'object',
            properties: {
              prompt: {
                type: 'string',
                description: 'The prompt to send to The Guru AI. Can use "guru, [model], prompt" syntax for direct model selection.',
              },
              model: {
                type: 'string',
                description: `Optional: Explicitly specify an AI model to use (e.g., "${this.availableModels.length > 0 ? this.availableModels[0] : "openai/gpt-4o"}"). Overrides task-based model selection.`,
                enum: this.availableModels.length > 0 ? this.availableModels : undefined,
              },
              stream: { type: 'boolean', description: 'Whether to stream the response. Default: true.', default: true },
              workflow: { type: 'boolean', description: 'Whether to process as a multi-step workflow. Default: false.', default: false }
            },
            required: ['prompt'],
          },
        },
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      try {
        if (request.params.name === 'guru_ai_request') {
          const args = request.params.arguments;
          if (!args || typeof args !== 'object' || args === null) {
            throw new McpError(ErrorCode.InvalidParams, 'Invalid arguments: must be an object.');
          }
          if (typeof args.prompt !== 'string' || !args.prompt.trim()) {
            throw new McpError(ErrorCode.InvalidParams, 'Prompt must be a non-empty string.');
          }
          
          const parsedCommand = this.parseGuruCommand(args.prompt);
          const effectivePrompt = parsedCommand.prompt;
          const isWorkflow = args.workflow || (parsedCommand.isGuruCommand && this.isWorkflowRequest(effectivePrompt));
          let preferredModel = parsedCommand.model || args.model;
          let modelToUse = this.selectBestModel(effectivePrompt, preferredModel);
          
          if (!this.isValidModel(modelToUse)) {
            console.error(`[Guru] Critical: Model ('${modelToUse}') selected by selectBestModel is NOT valid/available. This implies an issue with selectBestModel's fallback or availableModels list.`);
            if (this.availableModels.length > 0 && this.isValidModel(this.availableModels[0])) {
                 console.warn(`[Guru] Overriding invalid model '${modelToUse}' with first overall available model '${this.availableModels[0]}'.`);
                 modelToUse = this.availableModels[0];
            } else {
                throw new McpError(
                    ErrorCode.InvalidParams,
                    `Failed to identify an available model. Attempted: '${preferredModel || modelToUse}'. No valid models could be resolved. Available: ${this.availableModels.join(', ') || 'none'}. Check server model configuration and OpenRouter availability.`
                );
            }
          }
          
          console.error(`[Guru] Processing request: Model=${modelToUse}, Workflow=${isWorkflow}, Stream=${args.stream !== false}, Prompt (first 50): "${effectivePrompt.substring(0, 50)}..."`);
          
          let resultText;
          if (isWorkflow) {
            resultText = await this.executeWorkflow(effectivePrompt);
          } else {
            const useStreaming = args.stream !== false;
            if (useStreaming) {
              try {
                const streamResponse = await this.openrouterClient.post(
                  '/chat/completions',
                  {
                    model: modelToUse,
                    messages: [{ role: "user", content: effectivePrompt }],
                    temperature: 0.7,
                    max_tokens: 4000, 
                    stream: true
                  },
                  { responseType: 'stream' }
                );
                resultText = await this.handleStreamingResponse(streamResponse);
              } catch (streamError) {
                console.error(`[Guru] Streaming request failed for model ${modelToUse}: ${streamError.message}. Falling back to non-streaming.`);
                resultText = await this.callOpenRouterModel(modelToUse, effectivePrompt);
              }
            } else {
              resultText = await this.callOpenRouterModel(modelToUse, effectivePrompt);
            }
          }
          
          const attribution = `\n\n[Response provided by The Guru AI using ${modelToUse}]`;
          return { content: [{ type: 'text', text: resultText + attribution }] };

        } else {
          throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${request.params.name}`);
        }
      } catch (error) {
        console.error(`[Guru] Error in CallToolRequest for ${request.params.name}:`, error.message, error.stack);
        if (error instanceof McpError) throw error;
        let errorMessage = 'An unexpected error occurred while processing your tool request.';
        if (axios.isAxiosError(error)) {
            errorMessage = `API Error (${error.config?.url}): ${error.response?.status || ''} ${error.response?.statusText || ''} - ${JSON.stringify(error.response?.data || error.message)}`;
        } else if (error instanceof Error) {
          errorMessage = error.message;
        }
        throw new McpError(ErrorCode.InternalError, errorMessage);
      }
    });
  }

  async run() {
    try {
      await this.loadBestLLMCategories();
      await this.fetchAvailableModels();
      this.setupToolHandlers();
      
      const transport = new StdioServerTransport();
      await this.server.connect(transport);
      console.error('[Guru] The Guru AI MCP server running on stdio and ready for requests.');
    } catch (error) {
      console.error('[Guru] Critical error during server startup:', error);
      process.exit(1);
    }
  }
}

const server = new GuruAIServer();
server.run().catch(error => {
  console.error('[Guru] Failed to start The Guru AI MCP server instance:', error);
  process.exit(1);
});