import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn } from 'child_process';

class MCPTestClient {
  constructor() {
    this.client = new Client(
      {
        name: "test-client",
        version: "1.0.0"
      },
      {
        capabilities: {}
      }
    );
  }

  async connect() {
    console.log('🚀 Starting MCP server...');
    
    // Spawn the MCP server process
    const serverProcess = spawn('node', ['index.js'], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // Create transport using the spawned process
    const transport = new StdioClientTransport({
      stdin: serverProcess.stdin,
      stdout: serverProcess.stdout
    });

    // Connect the client
    await this.client.connect(transport);
    console.log('✅ Connected to MCP server');

    // Handle server process errors
    serverProcess.stderr.on('data', (data) => {
      console.log('📝 Server log:', data.toString());
    });

    serverProcess.on('error', (error) => {
      console.error('❌ Server process error:', error);
    });

    return serverProcess;
  }

  async listTools() {
    console.log('\n📋 Listing available tools...');
    try {
      const result = await this.client.listTools();
      console.log('✅ Available tools:', JSON.stringify(result, null, 2));
      return result;
    } catch (error) {
      console.error('❌ Error listing tools:', error);
      throw error;
    }
  }

  async testGuruRequest(prompt, options = {}) {
    console.log(`\n🧠 Testing Guru AI request: "${prompt.substring(0, 50)}..."`);
    try {
      const result = await this.client.callTool({
        name: 'guru_ai_request',
        arguments: {
          prompt: prompt,
          ...options
        }
      });
      
      console.log('✅ Guru AI response:');
      if (result.content && result.content[0] && result.content[0].text) {
        console.log(result.content[0].text);
      } else {
        console.log('Full result:', JSON.stringify(result, null, 2));
      }
      return result;
    } catch (error) {
      console.error('❌ Error calling guru_ai_request:', error);
      throw error;
    }
  }

  async runTests() {
    let serverProcess;
    try {
      // Connect to server
      serverProcess = await this.connect();

      // Test 1: List tools
      await this.listTools();

      // Test 2: Simple request
      await this.testGuruRequest("What is 2+2?");

      // Test 3: Code generation request
      await this.testGuruRequest("Write a simple JavaScript function to calculate fibonacci numbers");

      // Test 4: Creative content request
      await this.testGuruRequest("Write a short poem about programming");

      // Test 5: Guru command with specific model
      await this.testGuruRequest("guru, anthropic/claude-3-sonnet: Explain quantum computing in simple terms");

      console.log('\n🎉 All tests completed successfully!');

    } catch (error) {
      console.error('❌ Test failed:', error);
    } finally {
      if (serverProcess) {
        console.log('\n🛑 Shutting down server...');
        serverProcess.kill();
      }
    }
  }
}

// Run the tests
const testClient = new MCPTestClient();
testClient.runTests().catch(console.error);
