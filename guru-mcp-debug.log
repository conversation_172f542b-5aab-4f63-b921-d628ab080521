[2025-06-01T13:42:56.643Z] [Guru] MCP Server Starting Up
[2025-06-01T13:42:56.645Z] [Guru] Server run sequence started.
[2025-06-01T13:42:56.646Z] [Guru] Loading best LLM categories
[2025-06-01T13:42:56.646Z] [Guru] Loaded 11 model categories from C:\Users\<USER>\Documents\Cline\MCP\New Master MCP Local\data\best_llm_per_category.json
[2025-06-01T13:42:56.647Z] [Guru] Fetching available models from OpenRouter...
[2025-06-01T13:42:56.812Z] [Guru] Loaded 323 available models from OpenRouter
[2025-06-01T13:42:56.813Z] [Guru] Setting up tool handlers
[2025-06-01T13:42:56.813Z] [Guru] The Guru AI MCP server connected and running on stdio.
[2025-06-01T13:48:00.924Z] [Guru] MCP Server Starting Up
[2025-06-01T13:48:00.926Z] [Guru] Server run sequence started.
[2025-06-01T13:48:00.927Z] [Guru] Loading best LLM categories
[2025-06-01T13:48:00.928Z] [Guru] Loaded 11 model categories from C:\Users\<USER>\Documents\Cline\MCP\New Master MCP Local\data\best_llm_per_category.json
[2025-06-01T13:48:00.928Z] [Guru] Fetching available models from OpenRouter...
[2025-06-01T13:48:01.072Z] [Guru] Loaded 323 available models from OpenRouter
[2025-06-01T13:48:01.073Z] [Guru] Setting up tool handlers
[2025-06-01T13:48:01.074Z] [Guru] The Guru AI MCP server connected and running on stdio.
[2025-06-01T13:49:13.043Z] [Guru] MCP Server Starting Up
[2025-06-01T13:49:13.045Z] [Guru] Server run sequence started.
[2025-06-01T13:49:13.045Z] [Guru] Loading best LLM categories
[2025-06-01T13:49:13.046Z] [Guru] Loaded 11 model categories from C:\Users\<USER>\Documents\Cline\MCP\New Master MCP Local\data\best_llm_per_category.json
[2025-06-01T13:49:13.046Z] [Guru] Fetching available models from OpenRouter...
[2025-06-01T13:49:13.214Z] [Guru] Loaded 323 available models from OpenRouter
[2025-06-01T13:49:13.216Z] [Guru] Setting up tool handlers
[2025-06-01T13:49:13.217Z] [Guru] The Guru AI MCP server connected and running on stdio.
[2025-06-01T13:50:48.318Z] [Guru] MCP Server Starting Up
[2025-06-01T13:50:48.320Z] [Guru] Server run sequence started.
[2025-06-01T13:50:48.321Z] [Guru] Loading best LLM categories
[2025-06-01T13:50:48.321Z] [Guru] Loaded 11 model categories from C:\Users\<USER>\Documents\Cline\MCP\New Master MCP Local\data\best_llm_per_category.json
[2025-06-01T13:50:48.322Z] [Guru] Fetching available models from OpenRouter...
[2025-06-01T13:50:48.552Z] [Guru] Loaded 323 available models from OpenRouter
[2025-06-01T13:50:48.553Z] [Guru] Setting up tool handlers
[2025-06-01T13:50:48.553Z] [Guru] The Guru AI MCP server connected and running on stdio.
[2025-06-01T14:08:15.032Z] [Guru] MCP Server Starting Up
[2025-06-01T14:08:15.034Z] [Guru] Server run sequence started.
[2025-06-01T14:08:15.035Z] [Guru] Loading best LLM categories
[2025-06-01T14:08:15.035Z] [Guru] Loaded 11 model categories from C:\Users\<USER>\Documents\Cline\MCP\New Master MCP Local\data\best_llm_per_category.json
[2025-06-01T14:08:15.036Z] [Guru] Fetching available models from OpenRouter...
[2025-06-01T14:08:15.197Z] [Guru] Loaded 323 available models from OpenRouter
[2025-06-01T14:08:15.198Z] [Guru] Setting up tool handlers
[2025-06-01T14:08:15.198Z] [Guru] The Guru AI MCP server connected and running on stdio.
[2025-06-01T14:09:32.310Z] [Guru] MCP Server Starting Up
[2025-06-01T14:09:32.312Z] [Guru] Server run sequence started.
[2025-06-01T14:09:32.312Z] [Guru] Loading best LLM categories
[2025-06-01T14:09:32.313Z] [Guru] Loaded 11 model categories from C:\Users\<USER>\Documents\Cline\MCP\New Master MCP Local\data\best_llm_per_category.json
[2025-06-01T14:09:32.313Z] [Guru] Fetching available models from OpenRouter...
[2025-06-01T14:09:32.470Z] [Guru] Loaded 323 available models from OpenRouter
[2025-06-01T14:09:32.471Z] [Guru] Setting up tool handlers
[2025-06-01T14:09:32.471Z] [Guru] The Guru AI MCP server connected and running on stdio.
[2025-06-01T14:09:55.598Z] [Guru] Server shutting down via SIGINT
[2025-06-01T14:11:27.656Z] [Guru] MCP Server Starting Up
[2025-06-01T14:11:27.659Z] [Guru] Server run sequence started.
[2025-06-01T14:11:27.659Z] [Guru] Loading best LLM categories
[2025-06-01T14:11:27.660Z] [Guru] Loaded 11 model categories from C:\Users\<USER>\Documents\Cline\MCP\New Master MCP Local\data\best_llm_per_category.json
[2025-06-01T14:11:27.660Z] [Guru] Fetching available models from OpenRouter...
[2025-06-01T14:11:27.858Z] [Guru] Loaded 323 available models from OpenRouter
[2025-06-01T14:11:27.860Z] [Guru] Setting up tool handlers
[2025-06-01T14:11:27.861Z] [Guru] The Guru AI MCP server connected and running on stdio.
[2025-06-01T14:11:27.877Z] [Guru] ListTools request received
[2025-06-01T14:11:27.890Z] [Guru] CallTool request received
{
  "name": "guru_ai_request",
  "args": {
    "prompt": "What is 2+2?"
  }
}
[2025-06-01T14:11:27.892Z] [Guru] Parsing command (first 100 chars): "What is 2+2?..."
[2025-06-01T14:11:27.893Z] [Guru] Not a Guru command.
[2025-06-01T14:11:27.894Z] [Guru] Determining best model for task (first 100 chars): "What is 2+2?..."
{
  "preferredModel": null
}
[2025-06-01T14:11:27.895Z] [Guru] Task categorized as: truthfulness_factual
[2025-06-01T14:11:27.896Z] [Guru] Selected model for category 'truthfulness_factual': openai/o1
[2025-06-01T14:11:27.897Z] [Guru] Request details: Model=openai/o1, Workflow=false, Prompt="What is 2+2?..."
[2025-06-01T14:11:27.898Z] [Guru] Attempting streaming request to openai/o1
[2025-06-01T14:11:28.429Z] [Guru] Processing streaming response
[2025-06-01T14:11:30.244Z] [Guru] Stream: Finished with reason: stop
[2025-06-01T14:11:30.260Z] [Guru] CallTool request received
{
  "name": "guru_ai_request",
  "args": {
    "prompt": "Write a simple JavaScript function to calculate fibonacci numbers"
  }
}
[2025-06-01T14:11:30.263Z] [Guru] Parsing command (first 100 chars): "Write a simple JavaScript function to calculate fibonacci numbers..."
[2025-06-01T14:11:30.266Z] [Guru] Not a Guru command.
[2025-06-01T14:11:30.269Z] [Guru] Determining best model for task (first 100 chars): "Write a simple JavaScript function to calculate fibonacci numbers..."
{
  "preferredModel": null
}
[2025-06-01T14:11:30.271Z] [Guru] Task categorized as: code_generation
[2025-06-01T14:11:30.273Z] [Guru] No suitable model from category list found or available. Using first available model.
[2025-06-01T14:11:30.275Z] [Guru] First available model is: deepseek/deepseek-r1-distill-qwen-7b
[2025-06-01T14:11:30.277Z] [Guru] Request details: Model=deepseek/deepseek-r1-distill-qwen-7b, Workflow=false, Prompt="Write a simple JavaScript function to calculate fi..."
[2025-06-01T14:11:30.278Z] [Guru] Attempting streaming request to deepseek/deepseek-r1-distill-qwen-7b
[2025-06-01T14:11:30.952Z] [Guru] Processing streaming response
[2025-06-01T14:11:41.203Z] [Guru] Stream: Finished with reason: stop
[2025-06-01T14:11:41.216Z] [Guru] CallTool request received
{
  "name": "guru_ai_request",
  "args": {
    "prompt": "Write a short poem about programming"
  }
}
[2025-06-01T14:11:41.218Z] [Guru] Parsing command (first 100 chars): "Write a short poem about programming..."
[2025-06-01T14:11:41.220Z] [Guru] Not a Guru command.
[2025-06-01T14:11:41.222Z] [Guru] Determining best model for task (first 100 chars): "Write a short poem about programming..."
{
  "preferredModel": null
}
[2025-06-01T14:11:41.225Z] [Guru] Task categorized as: code_generation
[2025-06-01T14:11:41.226Z] [Guru] No suitable model from category list found or available. Using first available model.
[2025-06-01T14:11:41.228Z] [Guru] First available model is: deepseek/deepseek-r1-distill-qwen-7b
[2025-06-01T14:11:41.229Z] [Guru] Request details: Model=deepseek/deepseek-r1-distill-qwen-7b, Workflow=false, Prompt="Write a short poem about programming..."
[2025-06-01T14:11:41.231Z] [Guru] Attempting streaming request to deepseek/deepseek-r1-distill-qwen-7b
[2025-06-01T14:11:41.955Z] [Guru] Processing streaming response
[2025-06-01T14:11:48.996Z] [Guru] Stream: Finished with reason: stop
[2025-06-01T14:11:49.011Z] [Guru] CallTool request received
{
  "name": "guru_ai_request",
  "args": {
    "prompt": "guru, anthropic/claude-3-sonnet: Explain quantum computing in simple terms"
  }
}
[2025-06-01T14:11:49.015Z] [Guru] Parsing command (first 100 chars): "guru, anthropic/claude-3-sonnet: Explain quantum computing in simple terms..."
[2025-06-01T14:11:49.032Z] [Guru] Parsed Guru command with model: anthropic/claude-3-sonnet
[2025-06-01T14:11:49.034Z] [Guru] Workflow request check for prompt (first 100 chars) " Explain quantum computing in simple terms...": false
[2025-06-01T14:11:49.035Z] [Guru] Request details: Model=anthropic/claude-3-sonnet, Workflow=false, Prompt=" Explain quantum computing in simple terms..."
[2025-06-01T14:11:49.035Z] [Guru] Attempting streaming request to anthropic/claude-3-sonnet
[2025-06-01T14:11:50.128Z] [Guru] Processing streaming response
[2025-06-01T14:11:59.254Z] [Guru] Stream: Finished with reason: stop
