import axios from 'axios';

/**
 * Example of making an HTTP request to the Guru AI service
 * This demonstrates both direct OpenRouter API usage and using the MCP server
 */

// Option 1: Direct request to OpenRouter API
async function directOpenRouterRequest() {
  const OPENROUTER_API_KEY = '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';
  const OPENROUTER_API_ENDPOINT = 'https://openrouter.ai/api/v1';
  
  try {
    console.log('Making direct request to OpenRouter API...');
    
    const response = await axios.post(
      `${OPENROUTER_API_ENDPOINT}/chat/completions`,
      {
        model: 'google/gemini-2.5-pro-preview', // You can change the model as needed
        messages: [{ role: "user", content: "Write a short poem about programming" }],
        temperature: 0.7,
        max_tokens: 2000
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'HTTP-Referer': 'https://theguru.ai',
          'X-Title': 'The Guru AI'
        }
      }
    );
    
    console.log('OpenRouter API Response:');
    console.log(response.data.choices[0].message.content);
    return response.data;
  } catch (error) {
    console.error('Error making direct OpenRouter request:', error.response?.data || error.message);
    throw error;
  }
}

// Option 2: Request to the local MCP server
async function mcpServerRequest() {
  try {
    console.log('Making request to MCP server...');
    
    // This would typically be sent through the MCP protocol
    // This is a simplified example of what the MCP client would do
    const mcpRequest = {
      jsonrpc: '2.0',
      id: '1',
      method: 'callTool',
      params: {
        name: 'guru_ai_request',
        arguments: {
          prompt: 'Write a short poem about programming',
          model: 'google/gemini-2.5-pro-preview', // Optional, will auto-select if not provided
          stream: false,
          workflow: false
        }
      }
    };
    
    // In a real scenario, this would be sent through the MCP transport
    // For demonstration, we're showing the request structure
    console.log('MCP Request that would be sent:');
    console.log(JSON.stringify(mcpRequest, null, 2));
    
    // To actually use this with the MCP server, you would use the MCP client SDK
    // Example: const response = await mcpClient.callTool('guru_ai_request', { prompt: '...' });
    
    return 'See the MCP documentation for how to properly send this request through the MCP protocol';
  } catch (error) {
    console.error('Error with MCP request example:', error);
    throw error;
  }
}

// Option 3: Request to The Guru's fallback API
async function guruFallbackRequest() {
  const GURU_API_ENDPOINT = 'https://63660191-5465-48d1-a384-83fe8ffc5b94-00-k5l1b9vlgd3n.janeway.replit.dev/api/v1/mcp';
  const GURU_API_KEY = 'Bearer 9ea0e4f8868020aacf2479d1b4514bee';
  
  try {
    console.log('Making request to Guru fallback API...');
    
    const response = await axios.post(
      GURU_API_ENDPOINT,
      {
        prompt: 'Guru, google/gemini-2.5-pro-preview: Write a short poem about programming'
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': GURU_API_KEY
        }
      }
    );
    
    console.log('Guru API Response:');
    console.log(response.data.response);
    return response.data;
  } catch (error) {
    console.error('Error making Guru fallback request:', error.response?.data || error.message);
    throw error;
  }
}

// Execute one of the example functions
async function main() {
  try {
    // Choose which example to run
    const example = process.argv[2] || 'openrouter';
    
    switch (example) {
      case 'openrouter':
        await directOpenRouterRequest();
        break;
      case 'mcp':
        await mcpServerRequest();
        break;
      case 'guru':
        await guruFallbackRequest();
        break;
      default:
        console.log('Please specify an example: openrouter, mcp, or guru');
    }
  } catch (error) {
    console.error('Error in main:', error.message);
  }
}

// Run the example
main();
