# Guru AI HTTP Request Examples

This repository contains examples of how to make HTTP requests to the Guru AI service using different methods.

## Files

- `index.js` - The main MCP server implementation for The Guru AI
- `guru-http-request.js` - Examples of making HTTP requests to the Guru AI service

## How to Use

The `guru-http-request.js` file contains three different approaches to interact with the Guru AI service:

1. **Direct OpenRouter API Request** - Makes a direct HTTP request to the OpenRouter API
2. **MCP Server Request** - Shows the structure of a request to the local MCP server
3. **Guru Fallback API Request** - Makes a request to The Guru's fallback API

### Running the Examples

To run the examples, use the following command:

```bash
node guru-http-request.js [example]
```

Where `[example]` is one of:
- `openrouter` - Run the direct OpenRouter API request example (default)
- `mcp` - Run the MCP server request example
- `guru` - Run the Guru fallback API request example

### Example Usage

```bash
# Run the OpenRouter API request example
node guru-http-request.js openrouter

# Run the MCP server request example
node guru-http-request.js mcp

# Run the Guru fallback API request example
node guru-http-request.js guru
```

## Notes

- The OpenRouter API key and Guru API key in the examples are placeholders and may need to be updated.
- The MCP server request example shows the structure of the request but doesn't actually send it through the MCP protocol. In a real scenario, you would use the MCP client SDK.
