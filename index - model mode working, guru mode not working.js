import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import axios from 'axios';
import fs from 'fs';
import path from 'path';

// OpenRouter API configuration
const OPENROUTER_API_ENDPOINT = 'https://openrouter.ai/api/v1';
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || '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';

// Fallback API endpoint (The Guru's own API)
const GURU_API_ENDPOINT = 'https://63660191-5465-48d1-a384-83fe8ffc5b94-00-k5l1b9vlgd3n.janeway.replit.dev/api/v1/mcp';
const GURU_API_KEY = 'Bearer 9ea0e4f8868020aacf2479d1b4514bee';

// Path to the Best LLM per category document
const BEST_LLM_PATH = path.join(process.cwd(), 'data', 'best_llm_per_category.json');

class GuruAIServer {
  constructor() {
    // Create a log file
    this.logFile = path.join(process.cwd(), 'guru-mcp-debug.log');
    
    // Helper function to log to both console and file
    this.log = (message, data = null) => {
      const timestamp = new Date().toISOString();
      const logEntry = `[${timestamp}] ${message}${data ? '\n' + JSON.stringify(data, null, 2) : ''}`;
      
      // Log to console (might not show in VS Code)
      console.error(logEntry);
      
      // Log to file (will definitely show)
      try {
        fs.appendFileSync(this.logFile, logEntry + '\n');
      } catch (err) {
        console.error('Failed to write to log file:', err);
      }
    };

    // Initialize with a startup message
    this.log('[Guru] MCP Server Starting Up');

    this.server = new Server(
      {
        name: 'The Guru AI',
        version: '1.0.0',
        description: 'An intelligent MCP server that provides access to the best AI models for any task',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    // Initialize API clients
    this.openrouterClient = axios.create({
      baseURL: OPENROUTER_API_ENDPOINT,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://theguru.ai',
        'X-Title': 'The Guru AI',
      },
    });

    this.guruClient = axios.create({
      baseURL: GURU_API_ENDPOINT,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': GURU_API_KEY,
      },
    });

    // Model information
    this.availableModels = []; // Will be populated on startup
    this.modelCategories = {}; // Will be loaded from best_llm_per_category.json
    
    this.server.onerror = (error) => {
      this.log('[Guru] MCP Server Error', error);
    };
    
    process.on('SIGINT', async () => {
      this.log('[Guru] Server shutting down via SIGINT');
      await this.server.close();
      process.exit(0);
    });
  }

  // Load the Best LLM per category document
  async loadBestLLMCategories() {
    this.log('[Guru] Loading best LLM categories');
    
    const userDefinedDefaultCategories = {
        "overall_performance": ["google/gemini-2.5-pro-preview"],
        "advanced_reasoning": ["openai/o3"],
        "code_generation": ["anthropic/claude-4-sonnet"],
        "language_understanding": ["openai/gpt-4.1"],
        "mathematical_scientific": ["openai/o3", "openai/o3-mini"],
        "creative_content": ["google/gemini-2.5-pro"],
        "conversational_fluency": ["google/gemini-2.5-pro-preview"],
        "multilingual_proficiency": ["google/gemini-2.0-flash"],
        "instruction_adherence": ["openai/o1-mini"],
        "truthfulness_factual": ["openai/o1", "openai/o3-mini-high"],
        "ai_safety_ethics": ["openai/o3"]
      };

    try {
      if (fs.existsSync(BEST_LLM_PATH)) {
        const data = fs.readFileSync(BEST_LLM_PATH, 'utf8');
        this.modelCategories = JSON.parse(data);
        this.log(`[Guru] Loaded ${Object.keys(this.modelCategories).length} model categories from ${BEST_LLM_PATH}`);
      } else {
        this.log(`[Guru] ${BEST_LLM_PATH} not found. Using user-defined default model categories.`);
        this.modelCategories = userDefinedDefaultCategories;
        
        if (!fs.existsSync(path.dirname(BEST_LLM_PATH))) {
          fs.mkdirSync(path.dirname(BEST_LLM_PATH), { recursive: true });
        }
        
        fs.writeFileSync(BEST_LLM_PATH, JSON.stringify(this.modelCategories, null, 2));
        this.log(`[Guru] Created default model categories file at ${BEST_LLM_PATH} with user-defined defaults.`);
      }
    } catch (error) {
      this.log(`[Guru] Error loading/creating best LLM categories from ${BEST_LLM_PATH}: ${error.message}`, error);
      this.modelCategories = userDefinedDefaultCategories;
      this.log(`[Guru] Using user-defined default model categories due to error.`);
    }
  }

  // Fetch available models from OpenRouter
  async fetchAvailableModels() {
    this.log(`[Guru] Fetching available models from OpenRouter...`);
    try {
      const response = await this.openrouterClient.get('/models');
      
      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        this.availableModels = response.data.data
          .filter(model => model.enabled !== false)
          .map(model => model.id);
        
        this.log(`[Guru] Loaded ${this.availableModels.length} available models from OpenRouter`);
      } else {
        this.log('[Guru] Invalid response format from OpenRouter when fetching models', response.data);
        throw new Error('Invalid response format from OpenRouter');
      }
    } catch (error) {
      this.log(`[Guru] Error fetching models from OpenRouter: ${error.message}`, error.response?.data || error);
      
      // Try fetching from The Guru API as fallback
      try {
        this.log(`[Guru] Trying fallback to Guru API for models`);
        const response = await axios.get(
          'https://63660191-5465-48d1-a384-83fe8ffc5b94-00-k5l1b9vlgd3n.janeway.replit.dev/api/models',
          {
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );

        if (response.data && Array.isArray(response.data)) {
          this.availableModels = response.data
            .filter(model => model.available)
            .map(model => model.name);
            
          this.log(`[Guru] Loaded ${this.availableModels.length} available models from Guru API (fallback)`);
        } else {
          this.log('[Guru] Invalid response format from Guru API (fallback) when fetching models', response.data);
          throw new Error('Invalid response format from Guru API');
        }
      } catch (fallbackError) {
        this.log(`[Guru] Error fetching models from Guru API (fallback): ${fallbackError.message}`, fallbackError.response?.data || fallbackError);
        
        this.availableModels = [
          "google/gemini-2.5-pro-preview", "openai/o3", "anthropic/claude-4-sonnet",
          "openai/gpt-4.1", "openai/o3-mini", "google/gemini-2.5-pro",
          "google/gemini-2.0-flash", "openai/o1-mini", "openai/o1",
          "openai/o3-mini-high", "openai/gpt-4o", "anthropic/claude-3-opus",
          "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku"
        ];
        this.log(`[Guru] Using hardcoded fallback list of ${this.availableModels.length} models`);
      }
    }
  }

  // Determine the best model for a given task
  getBestModelForTask(task, preferredModel = null) {
    this.log(`[Guru] Determining best model for task (first 100 chars): "${task.substring(0, 100)}..."`, { preferredModel });
    
    if (preferredModel && this.isValidModel(preferredModel)) {
      this.log(`[Guru] Using user-preferred model: ${preferredModel}`);
      return preferredModel;
    }
  
    const taskCategory = this.analyzeTaskCategory(task);
    this.log(`[Guru] Task categorized as: ${taskCategory}`);
  
    let bestModels = this.modelCategories[taskCategory];
  
    if (!Array.isArray(bestModels) || bestModels.length === 0) {
      this.log(`[Guru] No models or invalid model array for category '${taskCategory}', trying 'overall_performance'.`);
      bestModels = this.modelCategories.overall_performance;
    }
  
    if (!Array.isArray(bestModels) || bestModels.length === 0) {
      this.log(`[Guru] No models or invalid model array for 'overall_performance', using hardcoded fallback.`);
      bestModels = ["google/gemini-2.5-pro-preview", "anthropic/claude-4-sonnet", "openai/gpt-4o"];
    }
  
    for (const model of bestModels) {
      if (this.isValidModel(model)) {
        this.log(`[Guru] Selected model for category '${taskCategory}': ${model}`);
        return model;
      }
    }
  
    this.log(`[Guru] No suitable model from category list found or available. Using first available model.`);
    if (this.availableModels && this.availableModels.length > 0) {
      this.log(`[Guru] First available model is: ${this.availableModels[0]}`);
      return this.availableModels[0];
    }
  
    const ultimateFallbackModel = "google/gemini-2.5-pro-preview";
    this.log(`[Guru] No models available at all. Using ultimate fallback model: ${ultimateFallbackModel}`);
    return ultimateFallbackModel;
  }

  // Analyze the task to determine its category
  analyzeTaskCategory(task) {
    const taskLower = task.toLowerCase();
    // ... (rest of analyzeTaskCategory logic is unchanged as it doesn't involve direct logging, but its result is logged by getBestModelForTask)
    if (taskLower.includes('code') || taskLower.includes('program') || taskLower.includes('function') || taskLower.includes('algorithm') || taskLower.includes('debug') || taskLower.includes('javascript') || taskLower.includes('python') || taskLower.includes('html') || taskLower.includes('css') || taskLower.includes('sql') || taskLower.includes('programming')) return "code_generation";
    if (taskLower.includes('write') || taskLower.includes('story') || taskLower.includes('creative') || taskLower.includes('poem') || taskLower.includes('novel') || taskLower.includes('blog') || taskLower.includes('article') || taskLower.includes('essay')) return "creative_content";
    if (taskLower.includes('math') || taskLower.includes('calculate') || taskLower.includes('equation') || taskLower.includes('formula') || taskLower.includes('statistics') || taskLower.includes('physics') || taskLower.includes('chemistry') || taskLower.includes('science') || taskLower.includes('research')) return "mathematical_scientific";
    if (taskLower.includes('analyze') || taskLower.includes('reasoning') || taskLower.includes('logic') || taskLower.includes('problem solving') || taskLower.includes('strategy') || taskLower.includes('plan') || taskLower.includes('complex')) return "advanced_reasoning";
    if (taskLower.includes('translate') || taskLower.includes('translation') || taskLower.includes('in french') || taskLower.includes('in spanish') || taskLower.includes('in german') || taskLower.includes('in chinese') || taskLower.includes('in japanese') || taskLower.includes('language')) return "multilingual_proficiency";
    if (taskLower.includes('fact') || taskLower.includes('what is') || taskLower.includes('when did') || taskLower.includes('who is') || taskLower.includes('explain') || taskLower.includes('define') || taskLower.includes('accurate') || taskLower.includes('truth')) return "truthfulness_factual";
    if (taskLower.includes('understand') || taskLower.includes('comprehend') || taskLower.includes('interpret') || taskLower.includes('meaning') || taskLower.includes('context')) return "language_understanding";
    if (taskLower.includes('follow') || taskLower.includes('exactly') || taskLower.includes('format') || taskLower.includes('according to') || taskLower.includes('instructions') || taskLower.includes('rules')) return "instruction_adherence";
    if (taskLower.includes('safe') || taskLower.includes('ethical') || taskLower.includes('moral') || taskLower.includes('responsible') || taskLower.includes('bias') || taskLower.includes('harm')) return "ai_safety_ethics";
    if (taskLower.includes('chat') || taskLower.includes('talk') || taskLower.includes('conversation') || taskLower.includes('discuss') || taskLower.includes('tell me about')) return "conversational_fluency";
    return "overall_performance";
  }

  // Parse the Guru command
  parseGuruCommand(prompt) {
    this.log(`[Guru] Parsing command (first 100 chars): "${prompt.substring(0, 100)}..."`);
    const guruPrefix = /^guru,\s*/i;
    if (!guruPrefix.test(prompt)) {
      this.log(`[Guru] Not a Guru command.`);
      return { isGuruCommand: false, model: null, prompt: prompt };
    }
    const withoutPrefix = prompt.replace(guruPrefix, '');
    for (const model of this.availableModels) {
      const modelPrefix = new RegExp(`^${model}[,:]\s*`, 'i');
      if (modelPrefix.test(withoutPrefix)) {
        this.log(`[Guru] Parsed Guru command with model: ${model}`);
        return { isGuruCommand: true, model: model, prompt: withoutPrefix.replace(modelPrefix, '') };
      }
    }
    this.log(`[Guru] Parsed Guru command without specific model.`);
    return { isGuruCommand: true, model: null, prompt: withoutPrefix };
  }

  // Check if a workflow is requested
  isWorkflowRequest(prompt) {
    const workflowKeywords = ['step by step', 'workflow', 'multi-step', 'sequence', 'chain', 'pipeline', 'process'];
    const isWorkflow = workflowKeywords.some(keyword => prompt.toLowerCase().includes(keyword));
    this.log(`[Guru] Workflow request check for prompt (first 100 chars) "${prompt.substring(0,100)}...": ${isWorkflow}`);
    return isWorkflow;
  }

  // Execute a multi-step workflow
  async executeWorkflow(prompt) {
    this.log(`[Guru] Executing workflow for (first 100 chars): "${prompt.substring(0, 100)}..."`);
    const workflowPlanPrompt = `
      I need to break down the following task into a sequence of steps, where each step might benefit
      from a different specialized AI model. For each step, specify the task category
      (code_generation, creative_content, truthfulness_factual, mathematical_scientific,
      multilingual_proficiency, advanced_reasoning, language_understanding, instruction_adherence,
      ai_safety_ethics, conversational_fluency, or overall_performance).
      Task: ${prompt}
      Format your response as a JSON array of steps, each with 'description' and 'category' fields.
      Example: [{"description": "Research key facts about X", "category": "truthfulness_factual"}, ...]`;
    
    try {
      const planningModel = "anthropic/claude-4-sonnet"; // Or another suitable model for planning
      this.log(`[Guru] Workflow: Using planning model ${planningModel}`);
      const planResponse = await this.callOpenRouterModel(planningModel, workflowPlanPrompt);
      
      const jsonMatch = planResponse.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        this.log("[Guru] Workflow: Failed to generate a valid JSON workflow plan from planning model.", { planResponse });
        throw new Error("Failed to generate a valid workflow plan");
      }
      
      const workflowSteps = JSON.parse(jsonMatch[0]);
      this.log(`[Guru] Workflow: Plan generated with ${workflowSteps.length} steps.`, workflowSteps);
      
      let stepResults = [];
      let contextSoFar = "";
      
      for (let i = 0; i < workflowSteps.length; i++) {
        const step = workflowSteps[i];
        this.log(`[Guru] Workflow: Executing step ${i+1}/${workflowSteps.length}: "${step.description}"`);
        const stepPrompt = `Task: ${step.description}\n\n${contextSoFar ? `Context from previous steps:\n${contextSoFar}` : ''}\nPlease complete this specific step of the larger task.`;
        
        const bestModel = this.getBestModelForTask(step.description); // Category is in step.category, but task description is better
        this.log(`[Guru] Workflow: Step ${i+1} will use model ${bestModel}`);
        
        const stepResult = await this.callOpenRouterModel(bestModel, stepPrompt);
        
        stepResults.push({ step: step.description, model: bestModel, result: stepResult });
        contextSoFar += `\n\nStep ${i+1} (${step.description}) result:\n${stepResult}`;
        this.log(`[Guru] Workflow: Completed step ${i+1} using ${bestModel}. Result length: ${stepResult.length}`);
      }
      
      let finalResponse = `I've completed your task "${prompt}" as a multi-step workflow:\n\n`;
      stepResults.forEach((result, index) => {
        finalResponse += `Step ${index+1}: ${result.step}\n(Using ${result.model})\n${result.result}\n\n`;
      });
      
      this.log(`[Guru] Workflow: Successfully completed for prompt "${prompt.substring(0,50)}..."`);
      return finalResponse;
    } catch (error) {
      this.log(`[Guru] Workflow execution error: ${error.message}. Falling back to single model.`, error);
      const bestModel = this.getBestModelForTask(prompt);
      return await this.callOpenRouterModel(bestModel, prompt);
    }
  }

  // Call a model through OpenRouter
  async callOpenRouterModel(model, prompt) {
    try {
      this.log(`[Guru] Calling model ${model} through OpenRouter`);
      this.log(`[Guru] Prompt (first 100 chars): ${prompt.substring(0, 100)}...`);
      
      const response = await this.openrouterClient.post('/chat/completions', {
        model: model,
        messages: [{ role: "user", content: prompt }],
        temperature: 0.7,
        max_tokens: 2000
      });
      
      this.log(`[Guru] OpenRouter response status: ${response.status}`);
      // Avoid logging full response data if it's too verbose, log relevant parts or a summary
      const responseSummary = {
          id: response.data?.id,
          model: response.data?.model,
          created: response.data?.created,
          usage: response.data?.usage,
          choicesCount: response.data?.choices?.length,
          firstChoiceMessageContentLength: response.data?.choices?.[0]?.message?.content?.length
      };
      this.log(`[Guru] OpenRouter response data (summary):`, responseSummary);
      
      if (response.data && response.data.choices && response.data.choices.length > 0 && response.data.choices[0].message && response.data.choices[0].message.content) {
        const content = response.data.choices[0].message.content;
        this.log(`[Guru] Successfully got content from ${model} (${content.length} chars): ${content.substring(0, 100)}...`);
        return content;
      } else {
        this.log(`[Guru] Invalid response structure from OpenRouter for model ${model}`, response.data);
        throw new Error('Invalid response format from OpenRouter');
      }
    } catch (error) {
      this.log(`[Guru] OpenRouter API error for model ${model}: ${error.message}`, error.response?.data || error);
      
      try {
        this.log(`[Guru] Trying fallback to Guru API for model ${model}`);
        const formattedPrompt = `Guru, ${model}: ${prompt}`;
        this.log(`[Guru] Fallback prompt (first 100 chars): ${formattedPrompt.substring(0, 100)}...`);
        
        const fallbackResponse = await this.guruClient.post('/', { prompt: formattedPrompt });
        
        this.log(`[Guru] Fallback Guru API response status: ${fallbackResponse.status}`);
        this.log(`[Guru] Fallback Guru API response data:`, fallbackResponse.data);
        
        if (fallbackResponse.data && fallbackResponse.data.response) {
          const content = fallbackResponse.data.response;
          this.log(`[Guru] Fallback Guru API success for ${model} (${content.length} chars): ${content.substring(0, 100)}...`);
          return content;
        } else {
          this.log(`[Guru] Invalid response format from fallback Guru API for model ${model}`, fallbackResponse.data);
          throw new Error('Invalid response format from The Guru API');
        }
      } catch (fallbackError) {
        this.log(`[Guru] Fallback Guru API error for model ${model}: ${fallbackError.message}`, fallbackError.response?.data || fallbackError);
        return `I apologize, but I encountered an error while processing your request. Both primary and fallback APIs are currently unavailable for model ${model}. Please try again later.\n\nPrimary Error: ${error.message}`;
      }
    }
  }

  // Check if the model is valid
  isValidModel(model) {
    const isValid = this.availableModels.includes(model);
    // This can be very chatty, so only log if it's an invalid model check or for debugging.
    // this.log(`[Guru] Model validation for ${model}: ${isValid}`);
    return isValid;
  }

  // Process streaming response with server-sent events
  async handleStreamingResponse(response) {
    try {
      this.log('[Guru] Processing streaming response');
      let fullResponse = '';
      return new Promise((resolve, reject) => {
        let buffer = '';
        response.data.on('data', (chunk) => {
          buffer += chunk.toString();
          while (buffer.includes('\n\n')) {
            const endOfLine = buffer.indexOf('\n\n');
            const line = buffer.substring(0, endOfLine);
            buffer = buffer.substring(endOfLine + 2);
            if (!line.trim()) continue;
            if (line.startsWith('data: ')) {
              try {
                // Check for special SSE termination signal from OpenRouter
                if (line.substring(6).trim() === '[DONE]') {
                    this.log('[Guru] Stream: Received [DONE] signal.');
                    // fullResponse might already be complete from choice.delta.content
                    // Resolve here if no other 'done' event is expected from the API.
                    // However, typical OpenRouter streams use a final JSON object with "choices": [{"finish_reason": "stop"}]
                    // For now, let's assume we rely on a proper JSON `data: {"done": true}` or the stream ending.
                    // If issues persist, this is a place to check OpenRouter's SSE format.
                    continue; 
                }
                const data = JSON.parse(line.substring(6));
                if (data.choices && data.choices[0].delta && data.choices[0].delta.content) {
                  fullResponse += data.choices[0].delta.content;
                }
                // Standard OpenAI-compatible stream has finish_reason
                if (data.choices && data.choices[0].finish_reason) {
                  this.log(`[Guru] Stream: Finished with reason: ${data.choices[0].finish_reason}`);
                  resolve(fullResponse); // Resolve when a finish reason is received
                  response.data.removeAllListeners(); // Clean up listeners
                  return; // Exit while loop and data handler
                }
                // A custom 'done' field handling, if applicable
                if (data.done) {
                  this.log('[Guru] Stream: Received done:true signal in data.');
                  resolve(fullResponse);
                  response.data.removeAllListeners();
                  return;
                }
              } catch (err) {
                this.log(`[Guru] Stream: Error parsing SSE data line: "${line}"`, err);
              }
            }
          }
        });
        response.data.on('error', (err) => {
          this.log(`[Guru] Stream error: ${err.message}`, err);
          reject(err);
        });
        response.data.on('end', () => {
          this.log(`[Guru] Stream ended. Accumulated response length: ${fullResponse.length}`);
          resolve(fullResponse); // Resolve with whatever was accumulated if stream ends before a clear "done"
        });
      });
    } catch (error) {
      this.log(`[Guru] Error in handleStreamingResponse setup: ${error.message}`, error);
      throw error;
    }
  }

  // Set up the tool handlers
  setupToolHandlers() {
    this.log('[Guru] Setting up tool handlers');
    
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      this.log('[Guru] ListTools request received');
      return {
        tools: [
          {
            name: 'guru_ai_request',
            description: 'Make a request to The Guru AI service with intelligent model selection',
            inputSchema: {
              type: 'object',
              properties: {
                prompt: { type: 'string', description: 'The prompt to send to The Guru AI. Can use "guru, [model], prompt" syntax.' },
                model: { type: 'string', description: 'Optional: Explicitly specify an AI model.', enum: this.availableModels },
                stream: { type: 'boolean', description: 'Whether to stream the response.', default: true },
                workflow: { type: 'boolean', description: 'Whether to process as a multi-step workflow.', default: false },
              },
              required: ['prompt'],
            },
          },
        ],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      this.log('[Guru] CallTool request received', { name: request.params.name, args: request.params.arguments });
      try {
        if (request.params.name === 'guru_ai_request') {
          const args = request.params.arguments;
          
          if (!args || typeof args !== 'object' || args === null) {
            this.log('[Guru] Invalid arguments format for guru_ai_request', args);
            throw new McpError(ErrorCode.InvalidParams, 'Invalid arguments format');
          }
          if (typeof args.prompt !== 'string' || !args.prompt) {
            this.log('[Guru] Prompt must be a non-empty string', args);
            throw new McpError(ErrorCode.InvalidParams, 'Prompt must be a non-empty string');
          }
          
          const parsedCommand = this.parseGuruCommand(args.prompt);
          const isWorkflow = args.workflow || (parsedCommand.isGuruCommand && this.isWorkflowRequest(parsedCommand.prompt));
          let modelToUse = parsedCommand.model || args.model;
          
          if (!modelToUse && !isWorkflow) { // Don't auto-select if it's a workflow that does its own model selection per step
            modelToUse = this.getBestModelForTask(parsedCommand.prompt);
          }
          
          if (modelToUse && !this.isValidModel(modelToUse)) { // Validate if a model is set (workflows might not set one initially at this stage)
            this.log(`[Guru] Invalid model specified: ${modelToUse}`, { available: this.availableModels });
            throw new McpError(ErrorCode.InvalidParams, `Invalid model: ${modelToUse}. Check available models.`);
          }
          
          this.log(`[Guru] Request details: Model=${modelToUse || 'Workflow-selected'}, Workflow=${isWorkflow}, Prompt="${parsedCommand.prompt.substring(0, 50)}..."`);
          
          let result;
          if (isWorkflow) {
            result = await this.executeWorkflow(parsedCommand.prompt);
          } else {
            if (!modelToUse) { // Should have been selected by getBestModelForTask if not workflow
                 modelToUse = this.getBestModelForTask(parsedCommand.prompt);
                 this.log(`[Guru] Auto-selected model for non-workflow task: ${modelToUse}`);
            }
            const useStreaming = args.stream !== false;
            if (useStreaming) {
              try {
                this.log(`[Guru] Attempting streaming request to ${modelToUse}`);
                const streamResponse = await this.openrouterClient.post(
                  '/chat/completions',
                  { model: modelToUse, messages: [{ role: "user", content: parsedCommand.prompt }], temperature: 0.7, max_tokens: 2000, stream: true },
                  { responseType: 'stream' }
                );
                result = await this.handleStreamingResponse(streamResponse);
              } catch (streamError) {
                this.log(`[Guru] Streaming error with ${modelToUse}: ${streamError.message}. Falling back to non-streaming.`, streamError);
                result = await this.callOpenRouterModel(modelToUse, parsedCommand.prompt);
              }
            } else {
              this.log(`[Guru] Making non-streaming request to ${modelToUse}`);
              result = await this.callOpenRouterModel(modelToUse, parsedCommand.prompt);
            }
          }
          
          const attribution = `\n\n[Response${isWorkflow ? ' (workflow)' : ''} provided by The Guru AI${modelToUse && !isWorkflow ? ' using ' + modelToUse : ''}]`;
          return { content: [{ type: 'text', text: result + attribution }] };
        } else {
          this.log(`[Guru] Unknown tool called: ${request.params.name}`);
          throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${request.params.name}`);
        }
      } catch (error) {
        let errorMessage = 'An unexpected error occurred in CallTool.';
        if (axios.isAxiosError(error)) {
          errorMessage = `API Error: ${error.response?.status} ${error.response?.data?.error?.message || error.response?.data?.message || error.message}`;
          this.log(`[Guru] Axios Error in CallTool (${request.params.name}): ${errorMessage}`, error.response?.data || error);
        } else if (error instanceof McpError) {
          this.log(`[Guru] McpError in CallTool (${request.params.name}): ${error.message}`, error);
          throw error; 
        } else if (error instanceof Error) {
          errorMessage = error.message;
          this.log(`[Guru] Generic Error in CallTool (${request.params.name}): ${errorMessage}`, error);
        }
        return { content: [{ type: 'text', text: `Error: ${errorMessage}` }], isError: true };
      }
    });
  }

  async run() {
    try {
      this.log('[Guru] Server run sequence started.');
      await this.loadBestLLMCategories();
      await this.fetchAvailableModels();
      this.setupToolHandlers();
      
      const transport = new StdioServerTransport();
      await this.server.connect(transport);
      this.log('[Guru] The Guru AI MCP server connected and running on stdio.');
    } catch (error) {
      this.log('[Guru] Critical error during server startup sequence:', error);
      process.exit(1);
    }
  }
}

const server = new GuruAIServer();
server.run().catch(error => {
  // Use console.error here as `server.log` might not be initialized if `new GuruAIServer()` failed.
  console.error('[Guru] Fatal: Failed to start The Guru AI MCP server (global catch):', error);
  process.exit(1);
});
