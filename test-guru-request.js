import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

/**
 * Example of using the MCP client to make a request to the Guru AI service
 */
async function main() {
  try {
    console.log('Starting Guru AI MCP server...');
    
    // Start the MCP server as a child process
    const serverProcess = spawn('node', ['index.js'], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: __dirname,
    });
    
    // Log server output for debugging
    serverProcess.stdout.on('data', (data) => {
      console.log(`Server stdout: ${data}`);
    });
    
    serverProcess.stderr.on('data', (data) => {
      console.error(`Server stderr: ${data}`);
    });
    
    // Create an MCP client that communicates with the server via stdio
    const transport = new StdioClientTransport({
      input: serverProcess.stdout,
      output: serverProcess.stdin,
    });
    
    const client = new Client();
    
    console.log('Connecting to Guru AI MCP server...');
    await client.connect(transport);
    
    console.log('Connected! Getting available tools...');
    const { tools } = await client.listTools();
    
    console.log('Available tools:');
    console.log(tools);
    
    // Make a request to the Guru AI service
    console.log('\nMaking a request to the Guru AI service...');
    const result = await client.callTool('guru_ai_request', {
      prompt: 'Write a short poem about programming',
      // Optional parameters:
      // model: 'google/gemini-2.5-pro-preview', // Specific model to use
      // stream: false, // Whether to stream the response
      // workflow: false, // Whether to process as a multi-step workflow
    });
    
    console.log('\nGuru AI Response:');
    console.log(result.content[0].text);
    
    // Clean up
    console.log('\nClosing connection and shutting down server...');
    await client.close();
    serverProcess.kill();
    
    console.log('Done!');
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the example
main();
