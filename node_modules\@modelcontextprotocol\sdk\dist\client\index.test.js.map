{"version": 3, "file": "index.test.js", "sourceRoot": "", "sources": ["../../src/client/index.test.ts"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,kDAAkD;AAClD,6DAA6D;AAC7D,OAAO,EAAE,MAAM,EAAE,MAAM,YAAY,CAAC;AACpC,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EACL,aAAa,EACb,kBAAkB,EAClB,YAAY,EACZ,uBAAuB,EACvB,2BAA2B,EAC3B,uBAAuB,EACvB,0BAA0B,EAC1B,sBAAsB,EACtB,0BAA0B,EAC1B,sBAAsB,EACtB,SAAS,GACV,MAAM,aAAa,CAAC;AAErB,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,iBAAiB,EAAE,MAAM,gBAAgB,CAAC;AAEnD,IAAI,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;IAClE,MAAM,eAAe,GAAc;QACjC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;QAC7C,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;QAC7C,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,OAAO,EAAE,EAAE;;YAC7C,IAAI,OAAO,CAAC,MAAM,KAAK,YAAY,EAAE,CAAC;gBACpC,MAAA,eAAe,CAAC,SAAS,gEAAG;oBAC1B,OAAO,EAAE,KAAK;oBACd,EAAE,EAAE,OAAO,CAAC,EAAE;oBACd,MAAM,EAAE;wBACN,eAAe,EAAE,uBAAuB;wBACxC,YAAY,EAAE,EAAE;wBAChB,UAAU,EAAE;4BACV,IAAI,EAAE,MAAM;4BACZ,OAAO,EAAE,KAAK;yBACf;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;YACD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC,CAAC;KACH,CAAC;IAEF,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB;QACE,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,KAAK;KACf,EACD;QACE,YAAY,EAAE;YACZ,QAAQ,EAAE,EAAE;SACb;KACF,CACF,CAAC;IAEF,MAAM,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAEtC,kDAAkD;IAClD,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAC/C,MAAM,CAAC,gBAAgB,CAAC;QACtB,MAAM,EAAE,YAAY;QACpB,MAAM,EAAE,MAAM,CAAC,gBAAgB,CAAC;YAC9B,eAAe,EAAE,uBAAuB;SACzC,CAAC;KACH,CAAC,CACH,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;IACzE,MAAM,WAAW,GAAG,2BAA2B,CAAC,CAAC,CAAC,CAAC;IACnD,MAAM,eAAe,GAAc;QACjC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;QAC7C,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;QAC7C,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,OAAO,EAAE,EAAE;;YAC7C,IAAI,OAAO,CAAC,MAAM,KAAK,YAAY,EAAE,CAAC;gBACpC,MAAA,eAAe,CAAC,SAAS,gEAAG;oBAC1B,OAAO,EAAE,KAAK;oBACd,EAAE,EAAE,OAAO,CAAC,EAAE;oBACd,MAAM,EAAE;wBACN,eAAe,EAAE,WAAW;wBAC5B,YAAY,EAAE,EAAE;wBAChB,UAAU,EAAE;4BACV,IAAI,EAAE,MAAM;4BACZ,OAAO,EAAE,KAAK;yBACf;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;YACD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC,CAAC;KACH,CAAC;IAEF,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB;QACE,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,KAAK;KACf,EACD;QACE,YAAY,EAAE;YACZ,QAAQ,EAAE,EAAE;SACb;KACF,CACF,CAAC;IAEF,MAAM,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAEtC,mDAAmD;IACnD,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC,OAAO,CAAC;QACxC,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,KAAK;KACf,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;IAC5D,MAAM,eAAe,GAAc;QACjC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;QAC7C,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;QAC7C,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,OAAO,EAAE,EAAE;;YAC7C,IAAI,OAAO,CAAC,MAAM,KAAK,YAAY,EAAE,CAAC;gBACpC,MAAA,eAAe,CAAC,SAAS,gEAAG;oBAC1B,OAAO,EAAE,KAAK;oBACd,EAAE,EAAE,OAAO,CAAC,EAAE;oBACd,MAAM,EAAE;wBACN,eAAe,EAAE,iBAAiB;wBAClC,YAAY,EAAE,EAAE;wBAChB,UAAU,EAAE;4BACV,IAAI,EAAE,MAAM;4BACZ,OAAO,EAAE,KAAK;yBACf;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;YACD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC,CAAC;KACH,CAAC;IAEF,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB;QACE,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,KAAK;KACf,EACD;QACE,YAAY,EAAE;YACZ,QAAQ,EAAE,EAAE;SACb;KACF,CACF,CAAC;IAEF,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAC3D,6DAA6D,CAC9D,CAAC;IAEF,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,CAAC;AACnD,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;IACpD,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB;QACE,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,KAAK;KACf,EACD;QACE,YAAY,EAAE;YACZ,SAAS,EAAE,EAAE;YACb,KAAK,EAAE,EAAE;SACV;KACF,CACF,CAAC;IAEF,MAAM,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QAC/D,eAAe,EAAE,uBAAuB;QACxC,YAAY,EAAE;YACZ,SAAS,EAAE,EAAE;YACb,KAAK,EAAE,EAAE;SACV;QACD,UAAU,EAAE;YACV,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,KAAK;SACf;KACF,CAAC,CAAC,CAAC;IAEJ,MAAM,CAAC,iBAAiB,CAAC,0BAA0B,EAAE,GAAG,EAAE,CAAC,CAAC;QAC1D,SAAS,EAAE,EAAE;KACd,CAAC,CAAC,CAAC;IAEJ,MAAM,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,GAAG,EAAE,CAAC,CAAC;QACtD,KAAK,EAAE,EAAE;KACV,CAAC,CAAC,CAAC;IAEJ,MAAM,CAAC,eAAe,EAAE,eAAe,CAAC,GACtC,iBAAiB,CAAC,gBAAgB,EAAE,CAAC;IAEvC,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB;QACE,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,KAAK;KACf,EACD;QACE,YAAY,EAAE;YACZ,QAAQ,EAAE,EAAE;SACb;QACD,yBAAyB,EAAE,IAAI;KAChC,CACF,CAAC;IAEF,MAAM,OAAO,CAAC,GAAG,CAAC;QAChB,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC;QAC/B,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC;KAChC,CAAC,CAAC;IAEH,uDAAuD;IACvD,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,CAAC,OAAO,CAAC;QAC7C,SAAS,EAAE,EAAE;QACb,KAAK,EAAE,EAAE;KACV,CAAC,CAAC;IAEH,oBAAoB;IACpB,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IAC5D,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IAExD,sDAAsD;IACtD,MAAM,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAChD,iCAAiC,CAClC,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;IACjE,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB;QACE,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,KAAK;KACf,EACD;QACE,YAAY,EAAE,EAAE;KACjB,CACF,CAAC;IAEF,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB;QACE,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,KAAK;KACf,EACD;QACE,YAAY,EAAE;YACZ,KAAK,EAAE;gBACL,WAAW,EAAE,IAAI;aAClB;SACF;KACF,CACF,CAAC;IAEF,MAAM,CAAC,eAAe,EAAE,eAAe,CAAC,GACtC,iBAAiB,CAAC,gBAAgB,EAAE,CAAC;IAEvC,MAAM,OAAO,CAAC,GAAG,CAAC;QAChB,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC;QAC/B,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC;KAChC,CAAC,CAAC;IAEH,2EAA2E;IAC3E,MAAM,MAAM,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IAEnE,+DAA+D;IAC/D,MAAM,uBAAuB,GAAG,IAAI,MAAM,CACxC;QACE,IAAI,EAAE,gCAAgC;QACtC,OAAO,EAAE,KAAK;KACf,EACD;QACE,YAAY,EAAE,EAAE;QAChB,yBAAyB,EAAE,IAAI;KAChC,CACF,CAAC;IAEF,MAAM,uBAAuB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAEvD,qFAAqF;IACrF,MAAM,MAAM,CAAC,uBAAuB,CAAC,oBAAoB,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAC1E,0BAA0B,CAC3B,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;IACjE,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB;QACE,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,KAAK;KACf,EACD;QACE,YAAY,EAAE;YACZ,OAAO,EAAE,EAAE;YACX,SAAS,EAAE;gBACT,WAAW,EAAE,IAAI;aAClB;SACF;KACF,CACF,CAAC;IAEF,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB;QACE,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,KAAK;KACf,EACD;QACE,YAAY,EAAE,EAAE;KACjB,CACF,CAAC;IAEF,MAAM,CAAC,eAAe,EAAE,eAAe,CAAC,GACtC,iBAAiB,CAAC,gBAAgB,EAAE,CAAC;IAEvC,MAAM,OAAO,CAAC,GAAG,CAAC;QAChB,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC;QAC/B,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC;KAChC,CAAC,CAAC;IAEH,0EAA0E;IAC1E,MAAM,MAAM,CACV,MAAM,CAAC,kBAAkB,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAC3D,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IACzB,MAAM,MAAM,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IAEtE,yEAAyE;IACzE,MAAM,MAAM,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CACxD,wDAAwD,CACzD,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,+DAA+D,EAAE,GAAG,EAAE;IACzE,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB;QACE,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,KAAK;KACf,EACD;QACE,YAAY,EAAE;YACZ,QAAQ,EAAE,EAAE;SACb;KACF,CACF,CAAC;IAEF,6DAA6D;IAC7D,MAAM,CAAC,GAAG,EAAE;QACV,MAAM,CAAC,iBAAiB,CAAC,0BAA0B,EAAE,GAAG,EAAE,CAAC,CAAC;YAC1D,KAAK,EAAE,YAAY;YACnB,IAAI,EAAE,WAAW;YACjB,OAAO,EAAE;gBACP,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,eAAe;aACtB;SACF,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IAEjB,uEAAuE;IACvE,MAAM,CAAC,GAAG,EAAE;QACV,MAAM,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/D,CAAC,CAAC,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;AACzD,CAAC,CAAC,CAAC;AAEH;;IAEI;AACJ,IAAI,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAC5B,MAAM,uBAAuB,GAAG,aAAa,CAAC,MAAM,CAAC;QACnD,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;QAChC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC;YACf,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE;SACjB,CAAC;KACH,CAAC,CAAC;IAEH,MAAM,wBAAwB,GAAG,aAAa,CAAC,MAAM,CAAC;QACpD,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC;QACrC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC;YACf,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE;YAChB,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE;SACjB,CAAC;KACH,CAAC,CAAC;IAEH,MAAM,iCAAiC,GAAG,kBAAkB,CAAC,MAAM,CAAC;QAClE,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;QAClC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC;YACf,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACtC,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE;SACpB,CAAC;KACH,CAAC,CAAC;IAEH,MAAM,oBAAoB,GAAG,uBAAuB,CAAC,EAAE,CACrD,wBAAwB,CACzB,CAAC;IACF,MAAM,yBAAyB,GAAG,iCAAiC,CAAC;IACpE,MAAM,mBAAmB,GAAG,YAAY,CAAC,MAAM,CAAC;QAC9C,WAAW,EAAE,CAAC,CAAC,MAAM,EAAE;QACvB,UAAU,EAAE,CAAC,CAAC,MAAM,EAAE;KACvB,CAAC,CAAC;IAMH,yCAAyC;IACzC,MAAM,aAAa,GAAG,IAAI,MAAM,CAK9B;QACE,IAAI,EAAE,eAAe;QACrB,OAAO,EAAE,OAAO;KACjB,EACD;QACE,YAAY,EAAE;YACZ,QAAQ,EAAE,EAAE;SACb;KACF,CACF,CAAC;IAEF,+EAA+E;IAC/E,KAAK;QACH,aAAa,CAAC,OAAO,CACnB;YACE,MAAM,EAAE,aAAa;YACrB,MAAM,EAAE;gBACN,IAAI,EAAE,SAAS;aAChB;SACF,EACD,mBAAmB,CACpB,CAAC;IAEJ,KAAK;QACH,aAAa,CAAC,YAAY,CAAC;YACzB,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE;gBACN,QAAQ,EAAE,SAAS;gBACnB,OAAO,EAAE,mBAAmB;aAC7B;SACF,CAAC,CAAC;AACP,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;IAC3D,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB;QACE,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,KAAK;KACf,EACD;QACE,YAAY,EAAE;YACZ,SAAS,EAAE,EAAE;SACd;KACF,CACF,CAAC;IAEF,qDAAqD;IACrD,MAAM,CAAC,iBAAiB,CACtB,0BAA0B,EAC1B,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;QACvB,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAC1D,OAAO;YACL,SAAS,EAAE,EAAE;SACd,CAAC;IACJ,CAAC,CACF,CAAC;IAEF,MAAM,CAAC,eAAe,EAAE,eAAe,CAAC,GACtC,iBAAiB,CAAC,gBAAgB,EAAE,CAAC;IAEvC,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB;QACE,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,KAAK;KACf,EACD;QACE,YAAY,EAAE,EAAE;KACjB,CACF,CAAC;IAEF,MAAM,OAAO,CAAC,GAAG,CAAC;QAChB,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC;QAC/B,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC;KAChC,CAAC,CAAC;IAEH,0BAA0B;IAC1B,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;IAEzC,0CAA0C;IAC1C,MAAM,oBAAoB,GAAG,MAAM,CAAC,aAAa,CAAC,SAAS,EAAE;QAC3D,MAAM,EAAE,UAAU,CAAC,MAAM;KAC1B,CAAC,CAAC;IACH,UAAU,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;IAEtC,6BAA6B;IAC7B,MAAM,MAAM,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AACvE,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;IAC/C,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB;QACE,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,KAAK;KACf,EACD;QACE,YAAY,EAAE;YACZ,SAAS,EAAE,EAAE;SACd;KACF,CACF,CAAC;IAEF,wCAAwC;IACxC,MAAM,CAAC,iBAAiB,CACtB,0BAA0B,EAC1B,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE;QACxB,MAAM,KAAK,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACpC,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACzC,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,CAAC;QACZ,OAAO;YACL,SAAS,EAAE,EAAE;SACd,CAAC;IACJ,CAAC,CACF,CAAC;IAEF,MAAM,CAAC,eAAe,EAAE,eAAe,CAAC,GACtC,iBAAiB,CAAC,gBAAgB,EAAE,CAAC;IAEvC,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB;QACE,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,KAAK;KACf,EACD;QACE,YAAY,EAAE,EAAE;KACjB,CACF,CAAC;IAEF,MAAM,OAAO,CAAC,GAAG,CAAC;QAChB,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC;QAC/B,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC;KAChC,CAAC,CAAC;IAEH,sDAAsD;IACtD,MAAM,MAAM,CACV,MAAM,CAAC,aAAa,CAAC,SAAS,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAChD,CAAC,OAAO,CAAC,aAAa,CAAC;QACtB,IAAI,EAAE,SAAS,CAAC,cAAc;KAC/B,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}