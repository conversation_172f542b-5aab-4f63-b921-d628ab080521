import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import axios from 'axios';
import fs from 'fs';
import path from 'path';

// OpenRouter API configuration
const OPENROUTER_API_ENDPOINT = 'https://openrouter.ai/api/v1';
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || '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';

// Fallback API endpoint (The Guru's own API)
const GURU_API_ENDPOINT = 'https://63660191-5465-48d1-a384-83fe8ffc5b94-00-k5l1b9vlgd3n.janeway.replit.dev/api/v1/mcp';
const GURU_API_KEY = 'Bearer 9ea0e4f8868020aacf2479d1b4514bee';

// Path to the Best LLM per category document
const BEST_LLM_PATH = path.join(process.cwd(), 'data', 'best_llm_per_category.json');

class GuruAIServer {
  constructor() {
    this.server = new Server(
      {
        name: 'The Guru AI',
        version: '1.0.0',
        description: 'An intelligent MCP server that provides access to the best AI models for any task',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    // Initialize API clients
    this.openrouterClient = axios.create({
      baseURL: OPENROUTER_API_ENDPOINT,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://theguru.ai',
        'X-Title': 'The Guru AI',
      },
    });

    this.guruClient = axios.create({
      baseURL: GURU_API_ENDPOINT,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': GURU_API_KEY,
      },
    });

    // Model information
    this.availableModels = []; // Will be populated on startup
    this.modelCategories = {}; // Will be loaded from best_llm_per_category.json
    
    this.server.onerror = (error) => console.error('[Guru MCP Error]', error);
    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  // Load the Best LLM per category document
  async loadBestLLMCategories() {
    try {
      // Check if the file exists
      if (fs.existsSync(BEST_LLM_PATH)) {
        const data = fs.readFileSync(BEST_LLM_PATH, 'utf8');
        this.modelCategories = JSON.parse(data);
        console.error(`[Guru] Loaded ${Object.keys(this.modelCategories).length} model categories`);
      } else {
        // Create a default categories file if it doesn't exist
        this.modelCategories = {
          "coding": ["openai/gpt-4o", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet"],
          "creative_writing": ["anthropic/claude-3-opus", "openai/gpt-4o", "meta-llama/llama-3-70b-instruct"],
          "factual_qa": ["anthropic/claude-3-opus", "openai/gpt-4o", "google/gemini-1.5-pro"],
          "summarization": ["anthropic/claude-3-sonnet", "openai/gpt-4o", "google/gemini-1.5-pro"],
          "translation": ["openai/gpt-4o", "anthropic/claude-3-sonnet", "meta-llama/llama-3-70b-instruct"],
          "default": ["anthropic/claude-3-sonnet", "openai/gpt-4o", "meta-llama/llama-3-70b-instruct"]
        };
        
        // Ensure the directory exists
        if (!fs.existsSync(path.dirname(BEST_LLM_PATH))) {
          fs.mkdirSync(path.dirname(BEST_LLM_PATH), { recursive: true });
        }
        
        // Write the default categories
        fs.writeFileSync(BEST_LLM_PATH, JSON.stringify(this.modelCategories, null, 2));
        console.error(`[Guru] Created default model categories file at ${BEST_LLM_PATH}`);
      }
    } catch (error) {
      console.error(`[Guru] Error loading best LLM categories: ${error.message}`);
      // Set default categories if loading fails
      this.modelCategories = {
        "default": ["anthropic/claude-3-sonnet", "openai/gpt-4o", "meta-llama/llama-3-70b-instruct"]
      };
    }
  }

  // Fetch available models from OpenRouter
  async fetchAvailableModels() {
    try {
      console.error(`[Guru] Fetching available models from OpenRouter...`);
      const response = await this.openrouterClient.get('/models');
      
      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        this.availableModels = response.data.data
          .filter(model => model.enabled !== false)
          .map(model => model.id);
        
        console.error(`[Guru] Loaded ${this.availableModels.length} available models from OpenRouter`);
      } else {
        throw new Error('Invalid response format from OpenRouter');
      }
    } catch (error) {
      console.error(`[Guru] Error fetching models from OpenRouter: ${error.message}`);
      
      // Try fetching from The Guru API as fallback
      try {
        const response = await axios.get(
          'https://63660191-5465-48d1-a384-83fe8ffc5b94-00-k5l1b9vlgd3n.janeway.replit.dev/api/models',
          {
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );

        if (response.data && Array.isArray(response.data)) {
          this.availableModels = response.data
            .filter(model => model.available)
            .map(model => model.name);
            
          console.error(`[Guru] Loaded ${this.availableModels.length} available models from Guru API`);
        } else {
          throw new Error('Invalid response format from Guru API');
        }
      } catch (fallbackError) {
        console.error(`[Guru] Error fetching models from Guru API: ${fallbackError.message}`);
        
        // Use a comprehensive fallback list of models
        this.availableModels = [
          "openai/gpt-4o",
          "anthropic/claude-3-opus",
          "anthropic/claude-3-sonnet",
          "anthropic/claude-3-haiku",
          "google/gemini-1.5-pro",
          "meta-llama/llama-3-70b-instruct",
          "meta-llama/llama-3-8b-instruct",
          "mistralai/mistral-large",
          "mistralai/mistral-medium",
          "mistralai/mistral-small"
        ];
        console.error(`[Guru] Using fallback list of ${this.availableModels.length} models`);
      }
    }
  }

  // Determine the best model for a given task
  getBestModelForTask(task, preferredModel = null) {
    // If user specified a model and it's available, use it
    if (preferredModel && this.isValidModel(preferredModel)) {
      return preferredModel;
    }
    
    // Analyze the task to determine its category
    const taskCategory = this.analyzeTaskCategory(task);
    
    // Get the best models for this category
    const bestModels = this.modelCategories[taskCategory] || this.modelCategories.default;
    
    // Find the first available model from the best models list
    for (const model of bestModels) {
      if (this.isValidModel(model)) {
        return model;
      }
    }
    
    // If no best model is available, return the first available model
    return this.availableModels[0] || "anthropic/claude-3-sonnet";
  }
  
  // Analyze the task to determine its category
  analyzeTaskCategory(task) {
    const taskLower = task.toLowerCase();
    
    // Simple keyword-based categorization
    if (taskLower.includes('code') || 
        taskLower.includes('program') || 
        taskLower.includes('function') || 
        taskLower.includes('algorithm') ||
        taskLower.includes('debug')) {
      return "coding";
    }
    
    if (taskLower.includes('write') || 
        taskLower.includes('story') || 
        taskLower.includes('creative') || 
        taskLower.includes('poem') ||
        taskLower.includes('novel')) {
      return "creative_writing";
    }
    
    if (taskLower.includes('fact') || 
        taskLower.includes('what is') || 
        taskLower.includes('when did') || 
        taskLower.includes('who is') ||
        taskLower.includes('explain')) {
      return "factual_qa";
    }
    
    if (taskLower.includes('summarize') || 
        taskLower.includes('summary') || 
        taskLower.includes('tldr') || 
        taskLower.includes('brief')) {
      return "summarization";
    }
    
    if (taskLower.includes('translate') || 
        taskLower.includes('translation') || 
        taskLower.includes('in french') || 
        taskLower.includes('in spanish') ||
        taskLower.includes('in german')) {
      return "translation";
    }
    
    // Default category if no specific category is detected
    return "default";
  }

  // Parse the Guru command
  parseGuruCommand(prompt) {
    // Format: "guru, [model], prompt" or "guru, prompt"
    const guruPrefix = /^guru,\s*/i;
    
    if (!guruPrefix.test(prompt)) {
      // Not a Guru command, return as is
      return {
        isGuruCommand: false,
        model: null,
        prompt: prompt
      };
    }
    
    // Remove the "guru," prefix
    const withoutPrefix = prompt.replace(guruPrefix, '');
    
    // Check if there's a model specified
    for (const model of this.availableModels) {
      const modelPrefix = new RegExp(`^${model}[,:]\s*`, 'i');
      if (modelPrefix.test(withoutPrefix)) {
        // Model is specified
        return {
          isGuruCommand: true,
          model: model,
          prompt: withoutPrefix.replace(modelPrefix, '')
        };
      }
    }
    
    // No model specified, just "guru, prompt"
    return {
      isGuruCommand: true,
      model: null,
      prompt: withoutPrefix
    };
  }

  // Check if a workflow is requested
  isWorkflowRequest(prompt) {
    const workflowKeywords = [
      'step by step',
      'workflow',
      'multi-step',
      'sequence',
      'chain',
      'pipeline',
      'process'
    ];
    
    return workflowKeywords.some(keyword => prompt.toLowerCase().includes(keyword));
  }

  // Execute a multi-step workflow
  async executeWorkflow(prompt) {
    // First, analyze the task to break it into steps
    const workflowPlanPrompt = `
      I need to break down the following task into a sequence of steps, where each step might benefit 
      from a different specialized AI model. For each step, specify the task category 
      (coding, creative_writing, factual_qa, summarization, translation, or default).
      
      Task: ${prompt}
      
      Format your response as a JSON array of steps, each with 'description' and 'category' fields.
      Example: [{"description": "Research key facts about X", "category": "factual_qa"}, ...]
    `;
    
    try {
      // Use a planning model to break down the task
      const planningModel = "anthropic/claude-3-sonnet";
      const planResponse = await this.callOpenRouterModel(planningModel, workflowPlanPrompt);
      
      // Extract the JSON workflow plan
      const jsonMatch = planResponse.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error("Failed to generate a valid workflow plan");
      }
      
      const workflowSteps = JSON.parse(jsonMatch[0]);
      
      // Execute each step with the appropriate model
      let stepResults = [];
      let contextSoFar = "";
      
      for (let i = 0; i < workflowSteps.length; i++) {
        const step = workflowSteps[i];
        const stepPrompt = `
          Task: ${step.description}
          
          ${contextSoFar ? `Context from previous steps:\n${contextSoFar}` : ''}
          
          Please complete this specific step of the larger task.
        `;
        
        // Get the best model for this step's category
        const bestModel = this.getBestModelForTask(step.description);
        
        // Execute the step
        const stepResult = await this.callOpenRouterModel(bestModel, stepPrompt);
        
        // Add to results and context
        stepResults.push({
          step: step.description,
          model: bestModel,
          result: stepResult
        });
        
        contextSoFar += `\n\nStep ${i+1} (${step.description}) result:\n${stepResult}`;
      }
      
      // Format the final response
      let finalResponse = `I've completed your task "${prompt}" as a multi-step workflow:\n\n`;
      
      stepResults.forEach((result, index) => {
        finalResponse += `Step ${index+1}: ${result.step}\n`;
        finalResponse += `(Using ${result.model})\n`;
        finalResponse += `${result.result}\n\n`;
      });
      
      return finalResponse;
    } catch (error) {
      console.error(`[Guru] Workflow execution error: ${error.message}`);
      // Fall back to single model execution
      const bestModel = this.getBestModelForTask(prompt);
      return await this.callOpenRouterModel(bestModel, prompt);
    }
  }

  // Call a model through OpenRouter
  async callOpenRouterModel(model, prompt) {
    try {
      console.error(`[Guru] Calling model ${model} through OpenRouter`);
      
      const response = await this.openrouterClient.post('/chat/completions', {
        model: model,
        messages: [
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000
      });
      
      if (response.data && 
          response.data.choices && 
          response.data.choices.length > 0 && 
          response.data.choices[0].message &&
          response.data.choices[0].message.content) {
        return response.data.choices[0].message.content;
      } else {
        throw new Error('Invalid response format from OpenRouter');
      }
    } catch (error) {
      console.error(`[Guru] OpenRouter API error: ${error.message}`);
      
      // Fall back to The Guru API
      try {
        const formattedPrompt = `Guru, ${model}: ${prompt}`;
        const fallbackResponse = await this.guruClient.post(
          '/',
          { prompt: formattedPrompt }
        );
        
        if (fallbackResponse.data && fallbackResponse.data.response) {
          return fallbackResponse.data.response;
        } else {
          throw new Error('Invalid response format from The Guru API');
        }
      } catch (fallbackError) {
        console.error(`[Guru] Fallback API error: ${fallbackError.message}`);
        return `I apologize, but I encountered an error while processing your request. Both OpenRouter and the fallback API are currently unavailable. Please try again later.`;
      }
    }
  }

  // Check if the model is valid
  isValidModel(model) {
    return this.availableModels.includes(model);
  }

  // Process streaming response with server-sent events
  async handleStreamingResponse(response) {
    try {
      console.error('[Guru] Processing streaming response');
      
      // Create a buffer to hold the full content
      let fullResponse = '';
      
      // Setup for processing the SSE stream
      return new Promise((resolve, reject) => {
        let buffer = '';
        
        // Handle the streaming data
        response.data.on('data', (chunk) => {
          // Append the new chunk to our buffer
          buffer += chunk.toString();
          
          // Process any complete messages in the buffer
          while (buffer.includes('\n\n')) {
            const endOfLine = buffer.indexOf('\n\n');
            const line = buffer.substring(0, endOfLine);
            buffer = buffer.substring(endOfLine + 2);
            
            // Skip empty lines
            if (!line.trim()) continue;
            
            // Handle data lines
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.substring(6));
                
                // Handle a chunk of content
                if (data.chunk) {
                  fullResponse += data.chunk;
                }
                
                // Handle end of stream
                if (data.done) {
                  return resolve(fullResponse);
                }
              } catch (err) {
                console.error(`[Guru] Error parsing SSE data: ${err.message}`);
              }
            }
          }
        });
        
        // Handle errors
        response.data.on('error', (err) => {
          console.error(`[Guru] Stream error: ${err.message}`);
          reject(err);
        });
        
        // Handle stream end
        response.data.on('end', () => {
          console.error(`[Guru] Stream ended`);
          resolve(fullResponse);
        });
      });
    } catch (error) {
      console.error(`[Guru] Error in streaming: ${error.message}`);
      throw error;
    }
  }

  // Set up the tool handlers
  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'guru_ai_request',
          description: 'Make a request to The Guru AI service with intelligent model selection',
          inputSchema: {
            type: 'object',
            properties: {
              prompt: {
                type: 'string',
                description: 'The prompt to send to The Guru AI. Can use "guru, [model], prompt" syntax for direct model selection.',
              },
              model: {
                type: 'string',
                description: 'Optional: Explicitly specify an AI model to use',
                enum: this.availableModels,
              },
              stream: {
                type: 'boolean',
                description: 'Whether to stream the response (true) or not (false)',
                default: true,
              },
              workflow: {
                type: 'boolean',
                description: 'Whether to process as a multi-step workflow',
                default: false,
              }
            },
            required: ['prompt'],
          },
        },
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      try {
        if (request.params.name === 'guru_ai_request') {
          const args = request.params.arguments;
          
          // Validate arguments
          if (!args || typeof args !== 'object' || args === null) {
            throw new McpError(ErrorCode.InvalidParams, 'Invalid arguments format');
          }
          
          if (typeof args.prompt !== 'string' || !args.prompt) {
            throw new McpError(ErrorCode.InvalidParams, 'Prompt must be a non-empty string');
          }
          
          // Parse the Guru command
          const parsedCommand = this.parseGuruCommand(args.prompt);
          
          // Determine if this is a workflow request
          const isWorkflow = args.workflow || 
                            (parsedCommand.isGuruCommand && this.isWorkflowRequest(parsedCommand.prompt));
          
          // Get the model to use (either from command, args, or auto-selected)
          let modelToUse = parsedCommand.model || args.model;
          
          if (!modelToUse) {
            // Auto-select the best model based on the task
            modelToUse = this.getBestModelForTask(parsedCommand.prompt);
          }
          
          // Validate the model
          if (!this.isValidModel(modelToUse)) {
            throw new McpError(
              ErrorCode.InvalidParams, 
              `Invalid model: ${modelToUse}. Available models: ${this.availableModels.slice(0, 3).join(', ')}${this.availableModels.length > 3 ? ', and more...' : ''}`
            );
          }
          
          console.error(`[Guru] Request details: Model=${modelToUse}, Workflow=${isWorkflow}, Prompt="${parsedCommand.prompt.substring(0, 50)}..."`);
          
          let result;
          
          // Execute as workflow or single model request
          if (isWorkflow) {
            result = await this.executeWorkflow(parsedCommand.prompt);
          } else {
            // Use streaming if requested
            const useStreaming = args.stream !== false;
            
            if (useStreaming) {
              try {
                // Make streaming request to OpenRouter
                const streamResponse = await this.openrouterClient.post(
                  '/chat/completions',
                  {
                    model: modelToUse,
                    messages: [{ role: "user", content: parsedCommand.prompt }],
                    temperature: 0.7,
                    max_tokens: 2000,
                    stream: true
                  },
                  { responseType: 'stream' }
                );
                
                result = await this.handleStreamingResponse(streamResponse);
              } catch (streamError) {
                console.error(`[Guru] Streaming error: ${streamError.message}`);
                // Fall back to non-streaming
                result = await this.callOpenRouterModel(modelToUse, parsedCommand.prompt);
              }
            } else {
              // Non-streaming request
              result = await this.callOpenRouterModel(modelToUse, parsedCommand.prompt);
            }
          }
          
          // Add a subtle attribution to the response
          const attribution = `\n\n[Response provided by The Guru AI using ${modelToUse}]`;
          
          // Return the result
          return { 
            content: [{ 
              type: 'text', 
              text: result + attribution 
            }] 
          };
        } else {
          throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${request.params.name}`);
        }
      } catch (error) {
        let errorMessage = 'An unexpected error occurred.';
        if (axios.isAxiosError(error)) {
          errorMessage = `API Error: ${error.response?.status} ${error.response?.data?.message || error.message}`;
        } else if (error instanceof McpError) {
          throw error; // Re-throw McpError directly
        } else if (error instanceof Error) {
          errorMessage = error.message;
        }
        console.error(`[Guru] Error calling tool ${request.params.name}:`, error);
        return {
          content: [{ type: 'text', text: errorMessage }],
          isError: true,
        };
      }
    });
  }

  async run() {
    try {
      // Load best LLM categories
      await this.loadBestLLMCategories();
      
      // Fetch available models
      await this.fetchAvailableModels();
      
      // Set up tool handlers after fetching models
      this.setupToolHandlers();
      
      const transport = new StdioServerTransport();
      await this.server.connect(transport);
      console.error('[Guru] The Guru AI MCP server running on stdio');
    } catch (error) {
      console.error('[Guru] Error during server startup:', error);
      process.exit(1);
    }
  }
}

const server = new GuruAIServer();
server.run().catch(error => {
  console.error('[Guru] Failed to start The Guru AI MCP server:', error);
  process.exit(1);
});

