import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

class SingleRequestTest {
  constructor() {
    this.client = new Client(
      {
        name: "single-test-client",
        version: "1.0.0"
      },
      {
        capabilities: {}
      }
    );
  }

  async connect() {
    console.log('🚀 Starting MCP server...');
    
    const transport = new StdioClientTransport({
      command: 'node',
      args: ['index.js']
    });

    await this.client.connect(transport);
    console.log('✅ Connected to MCP server');
    return transport;
  }

  async testRequest(prompt) {
    console.log(`\n🧠 Testing request: "${prompt}"`);
    try {
      const result = await this.client.callTool({
        name: 'guru_ai_request',
        arguments: {
          prompt: prompt
        }
      });
      
      console.log('✅ Response:');
      if (result.content && result.content[0] && result.content[0].text) {
        console.log(result.content[0].text);
      } else {
        console.log('Full result:', JSON.stringify(result, null, 2));
      }
      return result;
    } catch (error) {
      console.error('❌ Error:', error);
      throw error;
    }
  }

  async run() {
    let transport;
    try {
      transport = await this.connect();
      await this.testRequest("Write a Python function to sort a list");
      console.log('\n🎉 Test completed!');
    } catch (error) {
      console.error('❌ Test failed:', error);
    } finally {
      if (transport) {
        console.log('\n🛑 Shutting down...');
        await this.client.close();
      }
    }
  }
}

const test = new SingleRequestTest();
test.run().catch(console.error);
