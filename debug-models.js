import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

class ModelDebugger {
  constructor() {
    this.client = new Client(
      {
        name: "model-debugger",
        version: "1.0.0"
      },
      {
        capabilities: {}
      }
    );
  }

  async connect() {
    console.log('🚀 Starting MCP server for model debugging...');
    
    const transport = new StdioClientTransport({
      command: 'node',
      args: ['index.js']
    });

    await this.client.connect(transport);
    console.log('✅ Connected to MCP server');
    return transport;
  }

  async debugModelSelection() {
    console.log('\n🔍 Testing model selection for code generation...');
    try {
      // This will trigger the model selection logic and we can see the logs
      const result = await this.client.callTool({
        name: 'guru_ai_request',
        arguments: {
          prompt: "Write a simple Python function"
        }
      });
      
      console.log('✅ Request completed');
      return result;
    } catch (error) {
      console.error('❌ Error:', error);
      throw error;
    }
  }

  async run() {
    let transport;
    try {
      transport = await this.connect();
      await this.debugModelSelection();
      console.log('\n🎉 Debug completed!');
    } catch (error) {
      console.error('❌ Debug failed:', error);
    } finally {
      if (transport) {
        console.log('\n🛑 Shutting down...');
        await this.client.close();
      }
    }
  }
}

const modelDebugger = new ModelDebugger();
modelDebugger.run().catch(console.error);
